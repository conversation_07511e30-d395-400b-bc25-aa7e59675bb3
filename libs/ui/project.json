{"name": "ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/ui/src", "prefix": "lib", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/ui", "main": "libs/ui/src/index.ts", "tsConfig": "libs/ui/tsconfig.lib.json", "assets": ["libs/ui/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/ui/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint"}}}