<div class="app-container">
  <!-- TeamViewer-style Top Bar -->
  <div class="top-bar">
    <div class="top-bar-content">
      <div class="app-info">
        <mat-icon class="app-icon">account_balance</mat-icon>
        <span class="app-title">{{ appTitle }}</span>
      </div>

      <div class="loan-info">
        <div class="loan-badge">
          <mat-icon>description</mat-icon>
          <span class="loan-number">Loan: {{ activeLoanNumber }}</span>
        </div>
      </div>

      <div class="top-actions">
        <button mat-icon-button class="action-btn">
          <mat-icon>search</mat-icon>
        </button>
        <button mat-icon-button class="action-btn">
          <mat-icon>notifications</mat-icon>
        </button>
        <button mat-icon-button class="action-btn">
          <mat-icon>account_circle</mat-icon>
        </button>
      </div>
    </div>
  </div>

  <div class="app-content">
    <!-- TeamViewer-style Left Sidebar -->
    <div class="sidebar-container">
      <div class="sidebar-panel">
        <div class="sidebar-header">
          <h3>Navigation</h3>
        </div>

        <div class="sidebar-nav">
          <div class="nav-section">
            <div class="nav-item active">
              <div class="nav-item-content">
                <mat-icon>home</mat-icon>
                <span>Home</span>
              </div>
            </div>

            <div class="nav-item">
              <div class="nav-item-content">
                <mat-icon>dashboard</mat-icon>
                <span>Dashboard</span>
              </div>
            </div>
          </div>

          <div class="nav-divider"></div>

          <div class="nav-section">
            <div class="nav-item">
              <div class="nav-item-content">
                <mat-icon>assessment</mat-icon>
                <span>Reports</span>
              </div>
            </div>

            <div class="nav-item">
              <div class="nav-item-content">
                <mat-icon>analytics</mat-icon>
                <span>Analytics</span>
              </div>
            </div>
          </div>

          <div class="nav-divider"></div>

          <div class="nav-section">
            <div class="nav-item">
              <div class="nav-item-content">
                <mat-icon>settings</mat-icon>
                <span>Settings</span>
              </div>
            </div>

            <div class="nav-item">
              <div class="nav-item-content">
                <mat-icon>help</mat-icon>
                <span>Help</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <router-outlet></router-outlet>
    </div>
  </div>

  <!-- Footer -->
  <div class="footer-bar">
    <div class="footer-content">
      <span class="status-indicator">Ready to connect</span>
      <span class="copyright">© 2024 Rocket Logic Ensemble. All rights reserved.</span>
    </div>
  </div>
</div>