// TeamViewer-inspired design with rounded edges and inset effects
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

// Top Bar - TeamViewer style
.top-bar {
  background: #ffffff;
  border-radius: 0 0 16px 16px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  margin: 8px 8px 0 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.top-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  height: 56px;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 12px;

  .app-icon {
    color: #2196F3;
    font-size: 24px;
    width: 24px;
    height: 24px;
  }

  .app-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}

.loan-info {
  .loan-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(145deg, #e3f2fd, #bbdefb);
    padding: 8px 16px;
    border-radius: 20px;
    box-shadow:
      inset 2px 2px 4px rgba(0, 0, 0, 0.1),
      inset -2px -2px 4px rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(33, 150, 243, 0.2);

    mat-icon {
      color: #1976D2;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .loan-number {
      font-weight: 600;
      color: #1976D2;
      font-size: 14px;
    }
  }
}

.top-actions {
  display: flex;
  gap: 4px;

  .action-btn {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    box-shadow:
      2px 2px 4px rgba(0, 0, 0, 0.1),
      inset 1px 1px 2px rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(145deg, #e9ecef, #dee2e6);
      box-shadow:
        1px 1px 2px rgba(0, 0, 0, 0.15),
        inset 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    mat-icon {
      color: #666;
      font-size: 20px;
    }
  }
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 8px;
  padding: 8px;
}

// Left Sidebar - TeamViewer style with inset design
.sidebar-container {
  width: 280px;
  overflow-y: auto;
}

.sidebar-panel {
  background: #ffffff;
  border-radius: 16px;
  height: 100%;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.sidebar-header {
  padding: 20px 20px 16px 20px;
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.5);

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.sidebar-nav {
  padding: 12px;
}

.nav-section {
  margin-bottom: 8px;
}

.nav-item {
  margin-bottom: 4px;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    .nav-item-content {
      background: linear-gradient(145deg, #f0f4f8, #e2e8f0);
      box-shadow:
        inset 1px 1px 3px rgba(0, 0, 0, 0.1),
        inset -1px -1px 3px rgba(255, 255, 255, 0.8);
    }
  }

  &.active {
    .nav-item-content {
      background: linear-gradient(145deg, #e3f2fd, #bbdefb);
      box-shadow:
        inset 2px 2px 4px rgba(0, 0, 0, 0.15),
        inset -2px -2px 4px rgba(255, 255, 255, 0.8);
      border: 1px solid rgba(33, 150, 243, 0.2);

      mat-icon {
        color: #1976D2;
      }

      span {
        color: #1976D2;
        font-weight: 600;
      }
    }
  }
}

.nav-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  background: linear-gradient(145deg, #ffffff, #f8f9fa);
  box-shadow:
    1px 1px 2px rgba(0, 0, 0, 0.05),
    inset 1px 1px 2px rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;

  mat-icon {
    color: #666;
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  span {
    font-size: 14px;
    font-weight: 500;
    color: #333;
  }
}

.nav-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  margin: 12px 16px;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
}

// Main Content Area
.main-content {
  flex: 1;
  overflow-y: auto;
  background: #ffffff;
  border-radius: 16px;
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.08);
  padding: 20px;
}

// Footer Bar - TeamViewer style
.footer-bar {
  background: #ffffff;
  border-radius: 16px 16px 0 0;
  box-shadow:
    0 -4px 12px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  margin: 0 8px 8px 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-bottom: none;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  height: 40px;

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #4caf50;
    font-weight: 500;

    &::before {
      content: '';
      width: 8px;
      height: 8px;
      background: #4caf50;
      border-radius: 50%;
      box-shadow: 0 0 4px rgba(76, 175, 80, 0.5);
    }
  }

  .copyright {
    font-size: 12px;
    color: #666;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .sidebar-container {
    width: 240px;
  }

  .top-bar-content {
    padding: 8px 16px;

    .app-title {
      font-size: 16px;
    }

    .loan-badge {
      padding: 6px 12px;

      .loan-number {
        font-size: 12px;
      }
    }
  }

  .nav-item-content {
    padding: 10px 14px;

    span {
      font-size: 13px;
    }
  }
}