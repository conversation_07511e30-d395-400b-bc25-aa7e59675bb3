# UI Library

Shared UI components, directives, and pipes for the Rocket Logic Ensemble platform.

## Overview

The UI library provides a collection of reusable, standardized UI elements used throughout the application. It ensures consistency in:

- Visual design and branding
- Component behavior
- User interaction patterns
- Accessibility standards

## Structure

The library is organized into the following directories:

```
ui/
├── src/
│   └── lib/
│       ├── components/       # Reusable UI components
│       ├── directives/       # Custom directives
│       ├── pipes/            # Custom pipes
│       ├── shell/            # Application shell components
│       └── styles/           # Global styles and theme definitions
```

## Key Components

### UI Components

The library provides various reusable UI components:

- **Data Display**: Tables, cards, charts
- **Forms**: Inputs, selectors, date pickers
- **Feedback**: Alerts, notifications, progress indicators
- **Navigation**: Menus, breadcrumbs, tabs
- **Layout**: Containers, grids, dividers

Each component is:
- Standalone with explicit imports
- Fully responsive
- Accessible (WCAG compliant)
- Thoroughly tested
- Documented with examples

### Shell Components

The shell components provide the application's layout structure:

- **AppShell**: Main application container with navigation
- **TopBar**: Application header with user controls
- **SideNav**: Side navigation with collapsible sections

### Directives

Custom directives enhance HTML elements with additional functionality:

- **ClickOutside**: Detects clicks outside an element
- **HasPermission**: Conditionally shows/hides elements based on user permissions
- **Highlight**: Adds highlighting effects to elements
- **Debounce**: Prevents rapid repeated triggering of events

### Pipes

Transform data for display in templates:

- **Truncate**: Shortens text with ellipsis
- **FormatCurrency**: Standardized currency formatting
- **FormatDate**: Consistent date formatting
- **SafeHtml**: Sanitizes HTML content

## Usage Guidelines

### Component Integration

Import individual components directly:

```typescript
import { DataTableComponent, AlertComponent } from '@rocket-logic-ensemble/ui';

@Component({
  // ...
  imports: [
    DataTableComponent,
    AlertComponent
  ]
})
export class MyFeatureComponent {}
```

### Styling

The library provides:

- A consistent color palette
- Typography standards
- Spacing system
- Angular Material theme customizations

Use the provided variables and mixins in your component styles:

```scss
@import '@rocket-logic-ensemble/ui/styles/variables';

.my-component {
  color: $primary-color;
  padding: $spacing-medium;
  font-family: $font-family-main;
}
```

### Best Practices

1. Use UI library components instead of creating new ones
2. Follow the established design language
3. Maintain responsive behavior
4. Ensure accessibility compliance
5. Add thorough documentation for new components

## Contributing

When adding new components to the library:

1. Follow the established naming conventions
2. Create standalone components with explicit imports
3. Include comprehensive tests
4. Document usage with examples
5. Ensure accessibility compliance
6. Review and test across different screen sizes

## Building

Run `nx build ui` to build the library.

## Running unit tests

Run `nx test ui` to execute the unit tests via Jest.