import { Loan, LoanStatus, LoanType, EmploymentStatus } from '../interfaces/loan.interface';

export class LoanModel implements Loan {
  id!: string;
  loanNumber!: string;
  borrowerName!: string;
  borrowerEmail?: string;
  loanAmount!: number;
  interestRate!: number;
  loanTerm!: number;
  status!: LoanStatus;
  applicationDate!: Date;
  approvalDate?: Date;
  closingDate?: Date;
  propertyAddress!: string;
  propertyValue!: number;
  downPayment!: number;
  monthlyPayment!: number;
  loanType!: LoanType;
  creditScore?: number;
  debtToIncomeRatio?: number;
  employmentStatus!: EmploymentStatus;
  annualIncome!: number;
  createdAt!: Date;
  updatedAt!: Date;

  constructor(data: Partial<Loan>) {
    Object.assign(this, data);
    
    // Calculate monthly payment if not provided
    if (!this.monthlyPayment && this.loanAmount && this.interestRate && this.loanTerm) {
      this.monthlyPayment = this.calculateMonthlyPayment();
    }
  }

  /**
   * Calculate monthly payment using standard mortgage formula
   */
  calculateMonthlyPayment(): number {
    const principal = this.loanAmount;
    const monthlyRate = this.interestRate / 100 / 12;
    const numberOfPayments = this.loanTerm;

    if (monthlyRate === 0) {
      return principal / numberOfPayments;
    }

    const monthlyPayment = principal * 
      (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) /
      (Math.pow(1 + monthlyRate, numberOfPayments) - 1);

    return Math.round(monthlyPayment * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Calculate loan-to-value ratio
   */
  getLoanToValueRatio(): number {
    return Math.round((this.loanAmount / this.propertyValue) * 100 * 100) / 100;
  }

  /**
   * Get total interest paid over the life of the loan
   */
  getTotalInterest(): number {
    const totalPayments = this.monthlyPayment * this.loanTerm;
    return Math.round((totalPayments - this.loanAmount) * 100) / 100;
  }

  /**
   * Check if loan is in an active status
   */
  isActive(): boolean {
    return [
      LoanStatus.SUBMITTED,
      LoanStatus.UNDER_REVIEW,
      LoanStatus.APPROVED,
      LoanStatus.FUNDED
    ].includes(this.status);
  }

  /**
   * Check if loan is completed (closed or funded)
   */
  isCompleted(): boolean {
    return [LoanStatus.FUNDED, LoanStatus.CLOSED].includes(this.status);
  }

  /**
   * Check if loan application is still pending
   */
  isPending(): boolean {
    return [
      LoanStatus.DRAFT,
      LoanStatus.SUBMITTED,
      LoanStatus.UNDER_REVIEW
    ].includes(this.status);
  }

  /**
   * Get a human-readable status description
   */
  getStatusDescription(): string {
    const statusDescriptions = {
      [LoanStatus.DRAFT]: 'Application in progress',
      [LoanStatus.SUBMITTED]: 'Application submitted for review',
      [LoanStatus.UNDER_REVIEW]: 'Under review by underwriting',
      [LoanStatus.APPROVED]: 'Approved and ready for funding',
      [LoanStatus.DENIED]: 'Application denied',
      [LoanStatus.FUNDED]: 'Loan funded successfully',
      [LoanStatus.CLOSED]: 'Loan closed',
      [LoanStatus.CANCELLED]: 'Application cancelled'
    };

    return statusDescriptions[this.status] || 'Unknown status';
  }

  /**
   * Get days since application
   */
  getDaysSinceApplication(): number {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - this.applicationDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}
