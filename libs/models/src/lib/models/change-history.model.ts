import { ChangeHistoryEntry, ChangeType, ChangeSeverity, ChangeCategory } from '../interfaces/change-history.interface';

export class ChangeHistoryModel implements ChangeHistoryEntry {
  id!: string;
  entityId!: string;
  entityType!: any;
  changeType!: ChangeType;
  fieldName?: string;
  oldValue?: any;
  newValue?: any;
  description!: string;
  timestamp!: Date;
  userId!: string;
  userName!: string;
  userRole!: string;
  metadata?: any;
  severity!: ChangeSeverity;
  category!: ChangeCategory;

  constructor(data: Partial<ChangeHistoryEntry>) {
    Object.assign(this, data);
  }

  get timeAgo(): string {
    const now = new Date();
    const diffMs = now.getTime() - this.timestamp.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    
    return this.timestamp.toLocaleDateString();
  }

  get formattedTimestamp(): string {
    return this.timestamp.toLocaleString();
  }

  get severityColor(): string {
    switch (this.severity) {
      case ChangeSeverity.LOW: return 'success';
      case ChangeSeverity.MEDIUM: return 'warning';
      case ChangeSeverity.HIGH: return 'error';
      case ChangeSeverity.CRITICAL: return 'error';
      default: return 'primary';
    }
  }

  get changeTypeIcon(): string {
    switch (this.changeType) {
      case ChangeType.CREATED: return 'add_circle';
      case ChangeType.UPDATED: return 'edit';
      case ChangeType.DELETED: return 'delete';
      case ChangeType.STATUS_CHANGED: return 'swap_horiz';
      case ChangeType.DOCUMENT_UPLOADED: return 'upload_file';
      case ChangeType.DOCUMENT_APPROVED: return 'check_circle';
      case ChangeType.DOCUMENT_REJECTED: return 'cancel';
      case ChangeType.COMMENT_ADDED: return 'comment';
      case ChangeType.ASSIGNED: return 'person_add';
      case ChangeType.UNASSIGNED: return 'person_remove';
      case ChangeType.APPROVED: return 'thumb_up';
      case ChangeType.DENIED: return 'thumb_down';
      case ChangeType.CONDITIONS_ADDED: return 'warning';
      case ChangeType.CONDITIONS_CLEARED: return 'check';
      default: return 'info';
    }
  }

  get hasValueChange(): boolean {
    return this.oldValue !== undefined && this.newValue !== undefined;
  }

  get formattedValueChange(): string {
    if (!this.hasValueChange) return '';
    
    const oldVal = this.formatValue(this.oldValue);
    const newVal = this.formatValue(this.newValue);
    
    return `${oldVal} → ${newVal}`;
  }

  private formatValue(value: any): string {
    if (value === null || value === undefined) return 'None';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'number') {
      // Format currency if it looks like a monetary value
      if (this.fieldName?.toLowerCase().includes('amount') || 
          this.fieldName?.toLowerCase().includes('payment') ||
          this.fieldName?.toLowerCase().includes('income')) {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(value);
      }
      return value.toLocaleString();
    }
    if (value instanceof Date) return value.toLocaleDateString();
    return String(value);
  }
}
