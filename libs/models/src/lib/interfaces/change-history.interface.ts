export interface ChangeHistoryEntry {
  id: string;
  entityId: string;
  entityType: EntityType;
  changeType: ChangeType;
  fieldName?: string;
  oldValue?: any;
  newValue?: any;
  description: string;
  timestamp: Date;
  userId: string;
  userName: string;
  userRole: string;
  metadata?: ChangeMetadata;
  severity: ChangeSeverity;
  category: ChangeCategory;
}

export enum EntityType {
  LOAN = 'LOAN',
  BORROWER = 'BORROWER',
  PROPERTY = 'PROPERTY',
  DOCUMENT = 'DOCUMENT',
  UNDERWRITING = 'UNDERWRITING',
  APPROVAL = 'APPROVAL'
}

export enum ChangeType {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  DELETED = 'DELETED',
  STATUS_CHANGED = 'STATUS_CHANGED',
  DOCUMENT_UPLOADED = 'DOCUMENT_UPLOADED',
  DOCUMENT_APPROVED = 'DOCUMENT_APPROVED',
  DOCUMENT_REJECTED = 'DOCUMENT_REJECTED',
  COMMENT_ADDED = 'COMMENT_ADDED',
  ASSIGNED = 'ASSIGNED',
  UNASSIGNED = 'UNASSIGNED',
  APPROVED = 'APPROVED',
  DENIED = 'DENIED',
  CONDITIONS_ADDED = 'CONDITIONS_ADDED',
  CONDITIONS_CLEARED = 'CONDITIONS_CLEARED'
}

export enum ChangeSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum ChangeCategory {
  SYSTEM = 'SYSTEM',
  USER_ACTION = 'USER_ACTION',
  AUTOMATED = 'AUTOMATED',
  INTEGRATION = 'INTEGRATION',
  WORKFLOW = 'WORKFLOW'
}

export interface ChangeMetadata {
  source?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  workflowStep?: string;
  automationRule?: string;
  integrationSource?: string;
  additionalData?: Record<string, any>;
}

export interface ChangeHistoryFilter {
  entityId?: string;
  entityType?: EntityType;
  changeType?: ChangeType[];
  severity?: ChangeSeverity[];
  category?: ChangeCategory[];
  userId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  fieldName?: string;
  limit?: number;
  offset?: number;
}

export interface ChangeHistoryResponse {
  entries: ChangeHistoryEntry[];
  totalCount: number;
  hasMore: boolean;
  nextOffset?: number;
}

export interface TimelineGroup {
  date: string;
  entries: ChangeHistoryEntry[];
}
