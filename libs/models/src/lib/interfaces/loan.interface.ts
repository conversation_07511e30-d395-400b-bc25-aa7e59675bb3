export interface Loan {
  id: string;
  loanNumber: string;
  borrowerName: string;
  borrowerEmail?: string;
  loanAmount: number;
  interestRate: number;
  loanTerm: number; // in months
  status: LoanStatus;
  applicationDate: Date;
  approvalDate?: Date;
  closingDate?: Date;
  propertyAddress: string;
  propertyValue: number;
  downPayment: number;
  monthlyPayment: number;
  loanType: LoanType;
  creditScore?: number;
  debtToIncomeRatio?: number;
  employmentStatus: EmploymentStatus;
  annualIncome: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum LoanStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  APPROVED = 'approved',
  DENIED = 'denied',
  FUNDED = 'funded',
  CLOSED = 'closed',
  CANCELLED = 'cancelled'
}

export enum LoanType {
  CONVENTIONAL = 'conventional',
  FHA = 'fha',
  VA = 'va',
  USDA = 'usda',
  JUMBO = 'jumbo',
  REFINANCE = 'refinance'
}

export enum EmploymentStatus {
  EMPLOYED = 'employed',
  SELF_EMPLOYED = 'self_employed',
  UNEMPLOYED = 'unemployed',
  RETIRED = 'retired',
  STUDENT = 'student'
}

export interface LoanSearchCriteria {
  loanNumber?: string;
  borrowerName?: string;
  borrowerEmail?: string;
  status?: LoanStatus;
  loanType?: LoanType;
  minAmount?: number;
  maxAmount?: number;
  applicationDateFrom?: Date;
  applicationDateTo?: Date;
}

export interface LoanSearchResult {
  loans: Loan[];
  totalCount: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface LoanDocument {
  id: string;
  loanId: string;
  documentType: DocumentType;
  fileName: string;
  fileSize: number;
  uploadedAt: Date;
  uploadedBy: string;
  status: DocumentStatus;
  required: boolean;
}

export enum DocumentType {
  INCOME_VERIFICATION = 'INCOME_VERIFICATION',
  BANK_STATEMENTS = 'BANK_STATEMENTS',
  TAX_RETURNS = 'TAX_RETURNS',
  EMPLOYMENT_VERIFICATION = 'EMPLOYMENT_VERIFICATION',
  APPRAISAL = 'APPRAISAL',
  TITLE_REPORT = 'TITLE_REPORT',
  INSURANCE = 'INSURANCE',
  PURCHASE_AGREEMENT = 'PURCHASE_AGREEMENT',
  OTHER = 'OTHER'
}

export enum DocumentStatus {
  PENDING = 'PENDING',
  RECEIVED = 'RECEIVED',
  REVIEWED = 'REVIEWED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}
