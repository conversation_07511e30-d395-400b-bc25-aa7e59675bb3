import { LoanStatus, LoanType } from '../interfaces/loan.interface';

export class LoanSearchDto {
  loanNumber?: string;
  borrowerName?: string;
  borrowerEmail?: string;
  status?: LoanStatus;
  loanType?: LoanType;
  minAmount?: number;
  maxAmount?: number;
  applicationDateFrom?: string; // ISO date string
  applicationDateTo?: string; // ISO date string
  page?: number = 1;
  pageSize?: number = 10;
  sortBy?: string = 'applicationDate';
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class LoanResponseDto {
  id!: string;
  loanNumber!: string;
  borrowerName!: string;
  borrowerEmail?: string;
  loanAmount!: number;
  interestRate!: number;
  loanTerm!: number;
  status!: LoanStatus;
  applicationDate!: string; // ISO date string
  approvalDate?: string; // ISO date string
  closingDate?: string; // ISO date string
  propertyAddress!: string;
  propertyValue!: number;
  downPayment!: number;
  monthlyPayment!: number;
  loanType!: LoanType;
  creditScore?: number;
  debtToIncomeRatio?: number;
  employmentStatus!: string;
  annualIncome!: number;
  createdAt!: string; // ISO date string
  updatedAt!: string; // ISO date string
}

export class LoanSearchResponseDto {
  loans!: LoanResponseDto[];
  totalCount!: number;
  page!: number;
  pageSize!: number;
  hasMore!: boolean;
}
