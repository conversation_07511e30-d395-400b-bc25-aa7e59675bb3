import { EntityType, ChangeType, ChangeSeverity, ChangeCategory } from '../interfaces/change-history.interface';

export class ChangeHistoryFilterDto {
  entityId?: string;
  entityType?: EntityType;
  changeType?: ChangeType[];
  severity?: ChangeSeverity[];
  category?: ChangeCategory[];
  userId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  fieldName?: string;
  limit?: number = 50;
  offset?: number = 0;
}
