# Models Library

Shared data models, interfaces, DTOs, and enums for the Rocket Logic Ensemble platform.

## Overview

The Models library serves as the single source of truth for data structures used throughout the application. It provides:

- Type definitions for domain entities
- Data Transfer Objects (DTOs) for API communication
- Interface definitions for consistency across the application
- Model classes with business logic and data transformation

## Structure

The library is organized into the following directories:

```
models/
├── src/
│   └── lib/
│       ├── dtos/             # Data Transfer Objects
│       ├── interfaces/       # Interface definitions
│       └── models/           # Model classes with business logic
```

## Key Components

### DTOs (Data Transfer Objects)

DTOs define the shape of data as it moves between the frontend and backend:

- **CreateLoanDto**: Data structure for creating a new loan
- **UpdateLoanDto**: Data structure for updating an existing loan
- **ChangeHistoryFilterDto**: Parameters for filtering change history

DTOs are designed to:
- Validate incoming data
- Support partial updates
- Provide type safety
- Document API contracts

### Interfaces

Interfaces define the core data structures of the application:

- **Loan**: Core loan entity structure
- **ChangeHistoryEntry**: Structure for tracking changes
- **LoanDocument**: Structure for loan-related documents

Interfaces include:
- Property definitions with types
- Optional/required markers
- Related enums for typed constants
- Documentation for each field

### Models

Model classes extend interfaces with business logic:

- **LoanModel**: Loan entity with calculation and status methods
- **ChangeHistoryModel**: Change history with formatting and display logic

Models provide:
- Data transformation methods
- Derived property calculations
- Business rule implementations
- Helper methods for UI display

## Usage Guidelines

### When to Use Each Type

- **Interfaces**: For defining data shapes and API contracts
- **DTOs**: For API request/response payloads
- **Models**: For entities that require business logic or transformation

### Best Practices

1. Keep models focused on their domain
2. Minimize dependencies between models
3. Use clear property names that reflect the business domain
4. Document complex properties or relationships
5. Use enums for constants rather than string literals
6. Maintain backward compatibility when extending interfaces

### Implementation Examples

#### Interface Definition

```typescript
export interface Loan {
  id: string;
  applicationNumber: string;
  borrowerName: string;
  loanAmount: number;
  status: LoanStatus;
  // ...other properties
}

export enum LoanStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  // ...other statuses
}
```

#### DTO Implementation

```typescript
export class CreateLoanDto {
  applicationNumber!: string;
  borrowerName!: string;
  loanAmount!: number;
  // ...other properties
}
```

#### Model with Business Logic

```typescript
export class LoanModel implements Loan {
  id: string;
  // ...other properties

  constructor(data: Partial<Loan>) {
    Object.assign(this, data);
  }

  get isApproved(): boolean {
    return this.status === LoanStatus.APPROVED;
  }

  calculateMonthlyPayment(): number {
    // Payment calculation logic
  }
}
```

## Integration

This library is used by both the frontend (ensemble-ui) and backend (ensemble-bff) applications to ensure consistent data structures throughout the platform.

## Building

Run `nx build models` to build the library.

## Running unit tests

Run `nx test models` to execute the unit tests via Jest.