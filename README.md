# Rocket Logic Ensemble

A modern loan management and reconciliation application designed to help support teams troubleshoot and resolve issues with loans across multiple systems.

## Application Overview

Rocket Logic Ensemble is a comprehensive tool for technical and non-technical support teams to help resolve issues with loans. It provides a unified interface for:

- Searching loans by identifier
- Viewing real-time loan state from different systems
- Tracking change history with a detailed timeline
- Editing loan state in multiple systems individually or atomically
- Validating and authorizing actions to ensure security

The primary goal is to keep loan state synchronized across systems, identify where loans get stuck in processing, and quickly diagnose deviations.

## Key Features

- **Loan Search**: Quickly find loans by various identifiers
- **Multi-system Visualization**: View loan state across different systems side-by-side
- **Change Timeline**: See a chronological history of modifications to a loan
- **Inline Editing**: Make corrective changes to loan data with validation
- **Atomic Updates**: Synchronize changes across multiple systems at once
- **Security Controls**: Role-based access controls for sensitive operations

## Project Structure

This project is built as an Nx monorepo containing:

- **Frontend**: Angular application (`ensemble-ui`)
- **Backend**: NestJS API service (`ensemble-bff`)
- **Shared Libraries**: Models, interfaces, and UI components

### Applications

- `ensemble-bff`: NestJS backend API service
- `ensemble-ui`: Angular frontend application
- E2E testing apps: `ensemble-bff-e2e`, `ensemble-ui-e2e`

### Libraries

- `libs/models`: Contains DTOs, interfaces, and model classes
- `libs/ui`: Contains reusable UI components, directives, and pipes

## Technology Stack

- **Frontend**: Angular 20.x
- **Backend**: NestJS 11.x 
- **Build System**: Nx 21.2.0
- **Testing**: Jest (unit tests), Playwright (E2E)

## Getting Started

### Development

To start the frontend and backend development servers:

```bash
# Start NestJS backend
nx serve ensemble-bff

# Start Angular frontend (runs on http://localhost:4200)
nx serve ensemble-ui
```

### Building

```bash
# Build Angular frontend
nx build ensemble-ui

# Build NestJS backend
nx build ensemble-bff
```

### Testing

```bash
# Run frontend tests
nx test ensemble-ui

# Run backend tests
nx test ensemble-bff

# Run frontend E2E tests
nx e2e ensemble-ui-e2e

# Run backend E2E tests
nx e2e ensemble-bff-e2e
```

## Documentation

More detailed documentation for specific features and components can be found in the respective README.md files in their feature folders.