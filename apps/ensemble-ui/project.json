{"name": "ensemble-ui", "$schema": "../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "ens", "sourceRoot": "apps/ensemble-ui/src", "tags": [], "targets": {"build": {"executor": "@angular/build:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/ensemble-ui", "index": "apps/ensemble-ui/src/index.html", "browser": "apps/ensemble-ui/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/ensemble-ui/tsconfig.app.json", "assets": [{"glob": "**/*", "input": "apps/ensemble-ui/public"}], "styles": ["apps/ensemble-ui/src/styles.scss"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"continuous": true, "executor": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "ensemble-ui:build:production"}, "development": {"buildTarget": "ensemble-ui:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular/build:extract-i18n", "options": {"buildTarget": "ensemble-ui:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/ensemble-ui/jest.config.ts"}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "ensemble-ui:build", "port": 4200, "staticFilePath": "dist/ensemble-ui/browser", "spa": true}}}}