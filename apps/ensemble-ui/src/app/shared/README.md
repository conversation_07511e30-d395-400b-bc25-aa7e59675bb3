# Shared

This directory contains shared utilities, components, directives, and pipes that are used throughout the application.

## Overview

The shared module provides common functionality that can be reused across different features of the application. It includes:

- Utility functions and services
- Common UI components
- Custom directives
- Pipes for data transformation
- Shared models and interfaces

## Structure

The shared directory is organized as follows:

```
shared/
├── components/      # Reusable UI components
├── directives/      # Custom directives
├── pipes/           # Custom pipes
├── services/        # Common services
└── utils/           # Utility functions
```

## Shared Components

These are small, reusable UI components that can be used across different pages:

- **LoadingSpinner**: Displays a loading indicator
- **ErrorMessage**: Standard error display
- **ConfirmDialog**: Reusable confirmation dialog
- **StatusBadge**: Displays status with appropriate styling
- **ActionButton**: Standardized button with loading state

## Shared Directives

Custom directives that extend HTML elements with additional behavior:

- **ClickOutside**: Detects clicks outside an element
- **Debounce**: Adds debounce functionality to events
- **HasPermission**: Conditionally shows/hides based on user permissions
- **AutoFocus**: Automatically focuses an input element

## Shared Pipes

Transform data for display in templates:

- **FormatCurrency**: Standardized currency formatting
- **FormatDate**: Consistent date formatting
- **FilterBy**: Generic list filtering
- **SortBy**: Generic list sorting
- **Highlight**: Highlights search terms in text

## Shared Services

Common services used across features:

- **ErrorHandlingService**: Centralized error handling and reporting
- **NotificationService**: Toast notifications and alerts
- **AuthService**: Authentication and authorization
- **ApiService**: Base HTTP service for API calls

## Shared Utils

Pure utility functions:

- **dateUtils**: Date manipulation and formatting
- **stringUtils**: String manipulation
- **numberUtils**: Number formatting and calculations
- **validationUtils**: Common validation functions
- **securityUtils**: Security-related helper functions

## Usage Guidelines

1. Place code in shared only if it's used by multiple features
2. Keep components small, focused, and configurable
3. Document all public APIs thoroughly
4. Write comprehensive unit tests
5. Avoid feature-specific logic in shared code
6. Maintain pure functions whenever possible