import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NotificationService, Notification } from '../../services/notification.service';

@Component({
  selector: 'ens-notification',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="notification-container">
      @for (notification of notifications(); track notification.id) {
        <div class="notification {{ notification.type }}" role="alert">
          <div class="notification-content">
            <span class="notification-message">{{ notification.message }}</span>
          </div>
          <button class="notification-close" (click)="removeNotification(notification.id)">×</button>
        </div>
      }
    </div>
  `,
  styles: [`
    .notification-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: 10px;
      max-width: 400px;
    }
    
    .notification {
      padding: 16px;
      border-radius: 4px;
      box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
      display: flex;
      align-items: flex-start;
      animation: slideIn 0.3s ease-in-out;
      margin-bottom: 10px;
    }
    
    .notification-content {
      flex: 1;
    }
    
    .notification-message {
      display: block;
      margin-right: 10px;
    }
    
    .notification-close {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      padding: 0 5px;
      margin-left: 10px;
      opacity: 0.7;
    }
    
    .notification-close:hover {
      opacity: 1;
    }
    
    .success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .warning {
      background-color: #fff3cd;
      color: #856404;
      border: 1px solid #ffeeba;
    }
    
    .info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  `]
})
export class NotificationComponent {
  private readonly notificationService = inject(NotificationService);

  notifications = this.notificationService.getNotifications();

  getNotifications() {
    return this.notificationService.getNotifications();
  }
  
  removeNotification(id: string): void {
    this.notificationService.removeNotification(id);
  }
}