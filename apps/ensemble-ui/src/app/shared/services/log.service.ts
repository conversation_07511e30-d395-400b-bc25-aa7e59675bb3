import { Injectable, signal } from '@angular/core';

export interface LogEntry {
  id: string;
  timestamp: Date;
  action: string;
  details: string;
  category: 'search' | 'update' | 'error' | 'system';
}

@Injectable({
  providedIn: 'root'
})
export class LogService {
  private logs = signal<LogEntry[]>([]);
  private readonly LOG_STORAGE_KEY = 'application_logs';
  private readonly MAX_LOGS = 100;
  
  constructor() {
    this.loadLogs();
  }
  
  getLogs() {
    return this.logs;
  }
  
  addLog(action: string, details: string, category: 'search' | 'update' | 'error' | 'system') {
    const id = Date.now().toString();
    const logEntry: LogEntry = {
      id,
      timestamp: new Date(),
      action,
      details,
      category
    };
    
    this.logs.update(logs => {
      const updated = [logEntry, ...logs];
      const limited = updated.slice(0, this.MAX_LOGS);
      
      // Store in local storage
      this.saveLogs(limited);
      
      return limited;
    });
    
    return id;
  }
  
  logSearch(loanNumber: string): void {
    this.addLog('Loan Search', `Searched for loan number: ${loanNumber}`, 'search');
  }
  
  logUpdate(system: string, field: string, previousValue: string, newValue: string, loanNumber: string): void {
    this.addLog(
      'Field Update',
      `Updated ${field} in ${system} for loan ${loanNumber} from "${previousValue}" to "${newValue}"`,
      'update'
    );
  }
  
  logError(error: string, source: string): void {
    this.addLog('Error', `${source}: ${error}`, 'error');
  }
  
  logSystemEvent(event: string): void {
    this.addLog('System', event, 'system');
  }
  
  clearLogs(): void {
    localStorage.removeItem(this.LOG_STORAGE_KEY);
    this.logs.set([]);
  }
  
  private loadLogs(): void {
    const storedLogs = localStorage.getItem(this.LOG_STORAGE_KEY);
    if (storedLogs) {
      try {
        const logEntries = JSON.parse(storedLogs) as LogEntry[];
        // Ensure timestamps are Date objects
        logEntries.forEach(log => log.timestamp = new Date(log.timestamp));
        this.logs.set(logEntries);
      } catch (error) {
        console.error('Error parsing logs', error);
        this.logs.set([]);
      }
    }
  }
  
  private saveLogs(logs: LogEntry[]): void {
    localStorage.setItem(this.LOG_STORAGE_KEY, JSON.stringify(logs));
  }
}