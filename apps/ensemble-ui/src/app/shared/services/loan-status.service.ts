import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, delay, forkJoin, map, of, tap } from 'rxjs';
import { ErrorHandlingService } from './error-handling.service';
import { NotificationService } from './notification.service';

export interface SystemStatus {
  system: string;
  statusCode: string;
  statusDescription: string;
  lastUpdated: Date;
  editableFields: EditableField[];
}

export interface EditableField {
  name: string;
  value: string;
  editable: boolean;
  type: 'text' | 'number' | 'date' | 'select';
  options?: string[]; // For select type
  lastUpdated: Date;
}

export interface LoanStatus {
  loanNumber: string;
  systems: SystemStatus[];
  lastFetched: Date;
}

@Injectable({
  providedIn: 'root'
})
export class LoanStatusService {
  private readonly http = inject(HttpClient);
  private readonly errorHandling = inject(ErrorHandlingService);
  private readonly notification = inject(NotificationService);
  
  // Systems that we fetch status from
  private systems = [
    { id: 'underwriting', name: 'Underwriting System' },
    { id: 'servicing', name: 'Loan Servicing' },
    { id: 'document', name: 'Document Management' },
    { id: 'payment', name: 'Payment Processing' }
  ];
  
  getLoanStatus(loanNumber: string): Observable<LoanStatus> {
    // In a real app, this would make an API call
    // Instead, we'll simulate fetching from multiple systems with delay
    
    // Fetch status from each system
    const systemObservables = this.systems.map(system => {
      return this.fetchSystemStatus(loanNumber, system.id, system.name);
    });
    
    // Combine all system statuses
    return forkJoin(systemObservables).pipe(
      map(systemStatuses => ({
        loanNumber,
        systems: systemStatuses,
        lastFetched: new Date()
      })),
      catchError(error => this.errorHandling.handleError(error, 'LoanStatus'))
    );
  }
  
  private fetchSystemStatus(loanNumber: string, systemId: string, systemName: string): Observable<SystemStatus> {
    // Simulate API call with random delay between 500ms and 3000ms
    const randomDelay = Math.floor(Math.random() * 2500) + 500;
    
    // Mock data for each system
    return of(this.generateMockSystemStatus(systemId, systemName)).pipe(
      delay(randomDelay),
      tap(() => {
        this.notification.info(`Status fetched from ${systemName}`);
      })
    );
  }
  
  private generateMockSystemStatus(systemId: string, systemName: string): SystemStatus {
    const now = new Date();
    const editableFields: EditableField[] = [];
    
    // Generate different editable fields based on the system
    switch (systemId) {
      case 'underwriting':
        editableFields.push(
          { name: 'Credit Score', value: '720', editable: true, type: 'number', lastUpdated: this.randomPastDate(now) },
          { name: 'Loan Officer', value: 'Sarah Johnson', editable: true, type: 'text', lastUpdated: this.randomPastDate(now) },
          { name: 'Approval Status', value: 'Approved', editable: true, type: 'select', options: ['Pending', 'Approved', 'Denied', 'Waiting for Documents'], lastUpdated: this.randomPastDate(now) }
        );
        break;
      case 'servicing':
        editableFields.push(
          { name: 'Principal Balance', value: '$245,000.00', editable: false, type: 'text', lastUpdated: this.randomPastDate(now) },
          { name: 'Next Payment Due', value: '07/15/2024', editable: true, type: 'date', lastUpdated: this.randomPastDate(now) },
          { name: 'Late Fee', value: '$0.00', editable: true, type: 'text', lastUpdated: this.randomPastDate(now) }
        );
        break;
      case 'document':
        editableFields.push(
          { name: 'Missing Documents', value: 'None', editable: true, type: 'text', lastUpdated: this.randomPastDate(now) },
          { name: 'Last Document', value: 'Income Verification', editable: false, type: 'text', lastUpdated: this.randomPastDate(now) },
          { name: 'Document Status', value: 'Complete', editable: true, type: 'select', options: ['Incomplete', 'Pending Review', 'Complete'], lastUpdated: this.randomPastDate(now) }
        );
        break;
      case 'payment':
        editableFields.push(
          { name: 'Last Payment', value: '$1,250.00', editable: false, type: 'text', lastUpdated: this.randomPastDate(now) },
          { name: 'Payment Method', value: 'Auto Draft', editable: true, type: 'select', options: ['Auto Draft', 'Check', 'Online Payment', 'Phone Payment'], lastUpdated: this.randomPastDate(now) },
          { name: 'Payment Frequency', value: 'Monthly', editable: true, type: 'select', options: ['Monthly', 'Bi-Weekly', 'Weekly'], lastUpdated: this.randomPastDate(now) }
        );
        break;
    }
    
    return {
      system: systemName,
      statusCode: this.getRandomStatusCode(),
      statusDescription: 'Normal Processing',
      lastUpdated: this.randomPastDate(now),
      editableFields
    };
  }
  
  private getRandomStatusCode(): string {
    const codes = ['ACTIVE', 'PROCESSING', 'COMPLETE', 'PENDING'];
    return codes[Math.floor(Math.random() * codes.length)];
  }
  
  private randomPastDate(now: Date): Date {
    // Random date between now and 14 days ago
    const randomDays = Math.floor(Math.random() * 14);
    const randomHours = Math.floor(Math.random() * 24);
    const randomMinutes = Math.floor(Math.random() * 60);
    
    const date = new Date(now);
    date.setDate(date.getDate() - randomDays);
    date.setHours(date.getHours() - randomHours);
    date.setMinutes(date.getMinutes() - randomMinutes);
    
    return date;
  }
  
  updateField(loanNumber: string, system: string, fieldName: string, newValue: string): Observable<boolean> {
    // Simulate API call with delay
    return of(true).pipe(
      delay(1000),
      tap(() => {
        this.notification.success(`Updated ${fieldName} in ${system}`);
      }),
      catchError(error => this.errorHandling.handleError(error, `${system} Update`))
    );
  }
}