import { Injectable, signal } from '@angular/core';

export interface Notification {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  timestamp: Date;
  duration?: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private notifications = signal<Notification[]>([]);

  getNotifications() {
    return this.notifications;
  }

  addNotification(message: string, type: 'success' | 'error' | 'info' | 'warning', duration = 5000) {
    const id = Date.now().toString();
    const notification: Notification = {
      id,
      message,
      type,
      timestamp: new Date(),
      duration
    };

    this.notifications.update(notifications => [...notifications, notification]);

    if (duration > 0) {
      setTimeout(() => {
        this.removeNotification(id);
      }, duration);
    }

    return id;
  }

  success(message: string, duration = 5000) {
    return this.addNotification(message, 'success', duration);
  }

  error(message: string, duration = 5000) {
    return this.addNotification(message, 'error', duration);
  }

  info(message: string, duration = 5000) {
    return this.addNotification(message, 'info', duration);
  }

  warning(message: string, duration = 5000) {
    return this.addNotification(message, 'warning', duration);
  }

  removeNotification(id: string) {
    this.notifications.update(notifications => 
      notifications.filter(notification => notification.id !== id)
    );
  }
}