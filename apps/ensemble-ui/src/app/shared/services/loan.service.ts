import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { 
  LoanSearchDto, 
  CreateLoanDto, 
  UpdateLoanDto, 
  LoanResponseDto, 
  LoanSearchResponseDto 
} from '@rocket-logic-ensemble/models';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class LoanService {
  private readonly baseUrl = `${environment.apiUrl}/api/loans`;
  
  // Recent searches cache
  private recentSearchesSubject = new BehaviorSubject<string[]>([]);
  public recentSearches$ = this.recentSearchesSubject.asObservable();

  // Current loan cache
  private currentLoanSubject = new BehaviorSubject<LoanResponseDto | null>(null);
  public currentLoan$ = this.currentLoanSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadRecentSearches();
  }

  /**
   * Search for loans with filters
   */
  searchLoans(searchCriteria: Partial<LoanSearchDto>): Observable<LoanSearchResponseDto> {
    let params = new HttpParams();

    // Add search parameters
    Object.keys(searchCriteria).forEach(key => {
      const value = searchCriteria[key as keyof LoanSearchDto];
      if (value !== undefined && value !== null && value !== '') {
        params = params.set(key, value.toString());
      }
    });

    return this.http.get<LoanSearchResponseDto>(`${this.baseUrl}/search`, { params });
  }

  /**
   * Find a loan by loan number
   */
  findLoanByNumber(loanNumber: string): Observable<LoanResponseDto> {
    return this.http.get<LoanResponseDto>(`${this.baseUrl}/by-number/${loanNumber}`)
      .pipe(
        tap(loan => {
          this.currentLoanSubject.next(loan);
          this.addToRecentSearches(loanNumber);
        })
      );
  }

  /**
   * Find a loan by ID
   */
  findLoanById(id: string): Observable<LoanResponseDto> {
    return this.http.get<LoanResponseDto>(`${this.baseUrl}/${id}`)
      .pipe(
        tap(loan => this.currentLoanSubject.next(loan))
      );
  }

  /**
   * Create a new loan
   */
  createLoan(loanData: CreateLoanDto): Observable<LoanResponseDto> {
    return this.http.post<LoanResponseDto>(this.baseUrl, loanData)
      .pipe(
        tap(loan => this.currentLoanSubject.next(loan))
      );
  }

  /**
   * Update an existing loan
   */
  updateLoan(id: string, loanData: UpdateLoanDto): Observable<LoanResponseDto> {
    return this.http.put<LoanResponseDto>(`${this.baseUrl}/${id}`, loanData)
      .pipe(
        tap(loan => this.currentLoanSubject.next(loan))
      );
  }

  /**
   * Quick search by loan number (for search input)
   */
  quickSearchByLoanNumber(loanNumber: string): Observable<LoanResponseDto> {
    return this.findLoanByNumber(loanNumber);
  }

  /**
   * Get recent searches from local storage
   */
  getRecentSearches(): string[] {
    return this.recentSearchesSubject.value;
  }

  /**
   * Add a loan number to recent searches
   */
  private addToRecentSearches(loanNumber: string): void {
    const currentSearches = this.getRecentSearches();
    const updatedSearches = [loanNumber, ...currentSearches.filter(s => s !== loanNumber)]
      .slice(0, 5); // Keep only the 5 most recent

    this.recentSearchesSubject.next(updatedSearches);
    this.saveRecentSearches(updatedSearches);
  }

  /**
   * Clear recent searches
   */
  clearRecentSearches(): void {
    this.recentSearchesSubject.next([]);
    localStorage.removeItem('recentLoanSearches');
  }

  /**
   * Load recent searches from local storage
   */
  private loadRecentSearches(): void {
    try {
      const stored = localStorage.getItem('recentLoanSearches');
      if (stored) {
        const searches = JSON.parse(stored);
        this.recentSearchesSubject.next(searches);
      }
    } catch (error) {
      console.warn('Failed to load recent searches from localStorage:', error);
    }
  }

  /**
   * Save recent searches to local storage
   */
  private saveRecentSearches(searches: string[]): void {
    try {
      localStorage.setItem('recentLoanSearches', JSON.stringify(searches));
    } catch (error) {
      console.warn('Failed to save recent searches to localStorage:', error);
    }
  }

  /**
   * Clear current loan cache
   */
  clearCurrentLoan(): void {
    this.currentLoanSubject.next(null);
  }

  /**
   * Get current loan value (synchronous)
   */
  getCurrentLoan(): LoanResponseDto | null {
    return this.currentLoanSubject.value;
  }

  /**
   * Check if a loan number is valid format (10 digits)
   */
  isValidLoanNumber(loanNumber: string): boolean {
    return /^\d{10}$/.test(loanNumber);
  }

  /**
   * Format loan number for display (add dashes)
   */
  formatLoanNumber(loanNumber: string): string {
    if (loanNumber.length === 10) {
      return `${loanNumber.slice(0, 3)}-${loanNumber.slice(3, 6)}-${loanNumber.slice(6)}`;
    }
    return loanNumber;
  }

  /**
   * Parse formatted loan number back to plain number
   */
  parseLoanNumber(formattedNumber: string): string {
    return formattedNumber.replace(/\D/g, '');
  }
}
