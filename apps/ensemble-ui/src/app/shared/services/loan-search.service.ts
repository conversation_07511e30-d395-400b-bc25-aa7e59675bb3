import { Injectable, inject, signal } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, map, of, tap, throwError } from 'rxjs';
import { ErrorHandlingService } from './error-handling.service';
import { NotificationService } from './notification.service';

export interface LoanSearch {
  loanNumber: string;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class LoanSearchService {
  private readonly http = inject(HttpClient);
  private readonly errorHandling = inject(ErrorHandlingService);
  private readonly notification = inject(NotificationService);
  
  private readonly SEARCH_HISTORY_KEY = 'loan_search_history';
  private readonly MAX_RECENT_SEARCHES = 10;
  
  recentSearches = signal<LoanSearch[]>([]);
  
  constructor() {
    this.loadRecentSearches();
  }
  
  validateLoanNumber(loanNumber: string): boolean {
    // Check if it's exactly 10 digits
    return /^\d{10}$/.test(loanNumber);
  }
  
  searchLoan(loanNumber: string): Observable<boolean> {
    if (!this.validateLoanNumber(loanNumber)) {
      this.notification.error('Invalid loan number. Must be exactly 10 digits.');
      return throwError(() => new Error('Invalid loan number'));
    }
    
    // For demonstration purposes, we're not making a real API call
    // In a real application, this would call an API endpoint
    // return this.http.get<boolean>(`/api/loans/validate/${loanNumber}`)
    
    // Instead, we'll simulate a successful search
    return of(true).pipe(
      tap(() => {
        this.addToRecentSearches(loanNumber);
        this.notification.success(`Loan ${loanNumber} found successfully.`);
      }),
      catchError(error => this.errorHandling.handleError(error, 'LoanSearch'))
    );
  }
  
  private loadRecentSearches(): void {
    const storedSearches = localStorage.getItem(this.SEARCH_HISTORY_KEY);
    if (storedSearches) {
      try {
        const searches = JSON.parse(storedSearches) as LoanSearch[];
        // Ensure timestamps are Date objects
        searches.forEach(search => search.timestamp = new Date(search.timestamp));
        this.recentSearches.set(searches);
      } catch (error) {
        console.error('Error parsing recent searches', error);
        this.recentSearches.set([]);
      }
    }
  }
  
  private addToRecentSearches(loanNumber: string): void {
    const search: LoanSearch = { 
      loanNumber, 
      timestamp: new Date() 
    };
    
    // Add to beginning of list and ensure no duplicates
    this.recentSearches.update(searches => {
      // Remove any existing entry with same loan number
      const filtered = searches.filter(s => s.loanNumber !== loanNumber);
      
      // Add new search to beginning
      const updated = [search, ...filtered];
      
      // Limit list size
      const limited = updated.slice(0, this.MAX_RECENT_SEARCHES);
      
      // Save to local storage
      localStorage.setItem(this.SEARCH_HISTORY_KEY, JSON.stringify(limited));
      
      return limited;
    });
  }
  
  clearRecentSearches(): void {
    localStorage.removeItem(this.SEARCH_HISTORY_KEY);
    this.recentSearches.set([]);
    this.notification.info('Recent loan searches cleared.');
  }
}