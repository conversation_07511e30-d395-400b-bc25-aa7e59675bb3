import { Injectable, inject } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { NotificationService } from './notification.service';
import { Observable, throwError } from 'rxjs';

export interface ErrorLog {
  timestamp: Date;
  message: string;
  source: string;
  stack?: string;
  httpStatus?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlingService {
  private readonly notificationService = inject(NotificationService);
  private errorLogs: ErrorLog[] = [];

  handleError(error: Error | HttpErrorResponse, source = 'Application'): Observable<never> {
    let errorMessage = '';
    let httpStatus: number | undefined;

    if (error instanceof HttpErrorResponse) {
      // Server or connection error
      httpStatus = error.status;
      if (error.error instanceof ErrorEvent) {
        // Client-side error
        errorMessage = `Client Error: ${error.error.message}`;
      } else {
        // Server-side error
        errorMessage = `Server Error: ${error.status} - ${error.message}`;
      }
    } else {
      // Standard JS error
      errorMessage = `Error: ${error.message}`;
    }

    // Log the error
    const errorLog: ErrorLog = {
      timestamp: new Date(),
      message: errorMessage,
      source,
      stack: error instanceof Error ? error.stack : undefined,
      httpStatus
    };

    this.errorLogs.push(errorLog);
    this.notificationService.error(errorMessage);

    // Return the error for further handling
    return throwError(() => error);
  }

  getErrorLogs(): ErrorLog[] {
    return [...this.errorLogs];
  }

  clearErrorLogs(): void {
    this.errorLogs = [];
  }
}