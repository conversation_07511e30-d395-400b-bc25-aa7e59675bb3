import { <PERSON>rror<PERSON><PERSON>ler, Injectable, inject } from '@angular/core';
import { NotificationService } from './notification.service';
import { LogService } from './log.service';

@Injectable({
  providedIn: 'root'
})
export class GlobalErrorHandlerService implements ErrorHandler {
  private notificationService = inject(NotificationService);
  private logService = inject(LogService);

  handleError(error: any): void {
    // Log the error
    this.logService.error('Global error caught:', error);

    // Determine error type and show appropriate notification
    if (this.isNetworkError(error)) {
      this.notificationService.showError(
        'Network Error',
        'Unable to connect to the server. Please check your connection and try again.'
      );
    } else if (this.isValidationError(error)) {
      this.notificationService.showWarning(
        'Validation Error',
        error.message || 'Please check your input and try again.'
      );
    } else if (this.isAuthenticationError(error)) {
      this.notificationService.showError(
        'Authentication Error',
        'Your session has expired. Please log in again.'
      );
      // Could trigger logout logic here
    } else {
      // Generic error
      this.notificationService.showError(
        'Unexpected Error',
        'An unexpected error occurred. Please try again or contact support if the problem persists.'
      );
    }
  }

  private isNetworkError(error: any): boolean {
    return error?.status === 0 || 
           error?.name === 'NetworkError' ||
           error?.message?.includes('network');
  }

  private isValidationError(error: any): boolean {
    return error?.status === 400 || 
           error?.name === 'ValidationError';
  }

  private isAuthenticationError(error: any): boolean {
    return error?.status === 401 || 
           error?.status === 403;
  }
}
