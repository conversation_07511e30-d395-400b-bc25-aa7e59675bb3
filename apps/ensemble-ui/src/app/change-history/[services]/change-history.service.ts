import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject, interval, switchMap, catchError, of } from 'rxjs';
import { 
  ChangeHistoryEntry, 
  ChangeHistoryFilter, 
  ChangeHistoryResponse,
  EntityType,
  ChangeType,
  ChangeSeverity,
  ChangeCategory
} from '@rocket-logic-ensemble/models';

@Injectable({
  providedIn: 'root'
})
export class ChangeHistoryService {
  private readonly baseUrl = '/api/change-history';
  private readonly pollingInterval = 30000; // 30 seconds

  // Real-time updates subject
  private changeHistorySubject = new BehaviorSubject<ChangeHistoryEntry[]>([]);
  public changeHistory$ = this.changeHistorySubject.asObservable();

  // Polling subscription
  private pollingSubscription?: any;

  constructor(private http: HttpClient) {}

  /**
   * Get change history with filters
   */
  getChangeHistory(filter: ChangeHistoryFilter): Observable<ChangeHistoryResponse> {
    const params = this.buildHttpParams(filter);
    
    // For now, return mock data. Replace with actual HTTP call:
    // return this.http.get<ChangeHistoryResponse>(`${this.baseUrl}`, { params });
    
    return of(this.generateMockResponse(filter));
  }

  /**
   * Get change history for a specific entity
   */
  getEntityChangeHistory(entityId: string, entityType: EntityType, limit = 50): Observable<ChangeHistoryEntry[]> {
    const filter: ChangeHistoryFilter = {
      entityId,
      entityType,
      limit
    };
    
    return this.getChangeHistory(filter).pipe(
      switchMap(response => of(response.entries)),
      catchError(error => {
        console.error('Error fetching change history:', error);
        return of([]);
      })
    );
  }

  /**
   * Start real-time polling for change history updates
   */
  startPolling(filter: ChangeHistoryFilter): void {
    this.stopPolling();
    
    this.pollingSubscription = interval(this.pollingInterval)
      .pipe(
        switchMap(() => this.getChangeHistory(filter)),
        catchError(error => {
          console.error('Error in change history polling:', error);
          return of({ entries: [], totalCount: 0, hasMore: false });
        })
      )
      .subscribe(response => {
        this.changeHistorySubject.next(response.entries);
      });
  }

  /**
   * Stop real-time polling
   */
  stopPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = undefined;
    }
  }

  /**
   * Export change history to CSV
   */
  exportChangeHistory(filter: ChangeHistoryFilter): Observable<Blob> {
    const params = this.buildHttpParams(filter);
    
    // For now, generate mock CSV. Replace with actual HTTP call:
    // return this.http.get(`${this.baseUrl}/export`, { 
    //   params, 
    //   responseType: 'blob' 
    // });
    
    return of(this.generateMockCsv(filter));
  }

  /**
   * Get change history statistics
   */
  getChangeHistoryStats(entityId: string, entityType: EntityType): Observable<any> {
    const params = new HttpParams()
      .set('entityId', entityId)
      .set('entityType', entityType);
    
    // For now, return mock stats. Replace with actual HTTP call:
    // return this.http.get(`${this.baseUrl}/stats`, { params });
    
    return of(this.generateMockStats());
  }

  private buildHttpParams(filter: ChangeHistoryFilter): HttpParams {
    let params = new HttpParams();
    
    if (filter.entityId) params = params.set('entityId', filter.entityId);
    if (filter.entityType) params = params.set('entityType', filter.entityType);
    if (filter.changeType?.length) {
      filter.changeType.forEach(type => {
        params = params.append('changeType', type);
      });
    }
    if (filter.severity?.length) {
      filter.severity.forEach(severity => {
        params = params.append('severity', severity);
      });
    }
    if (filter.category?.length) {
      filter.category.forEach(category => {
        params = params.append('category', category);
      });
    }
    if (filter.userId) params = params.set('userId', filter.userId);
    if (filter.dateFrom) params = params.set('dateFrom', filter.dateFrom.toISOString());
    if (filter.dateTo) params = params.set('dateTo', filter.dateTo.toISOString());
    if (filter.fieldName) params = params.set('fieldName', filter.fieldName);
    if (filter.limit) params = params.set('limit', filter.limit.toString());
    if (filter.offset) params = params.set('offset', filter.offset.toString());
    
    return params;
  }

  private generateMockResponse(filter: ChangeHistoryFilter): ChangeHistoryResponse {
    const mockEntries: ChangeHistoryEntry[] = [
      {
        id: '1',
        entityId: filter.entityId || 'loan-123',
        entityType: filter.entityType || EntityType.LOAN,
        changeType: ChangeType.STATUS_CHANGED,
        fieldName: 'status',
        oldValue: 'UNDER_REVIEW',
        newValue: 'APPROVED',
        description: 'Loan status changed from Under Review to Approved',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        userId: 'user1',
        userName: 'John Smith',
        userRole: 'Underwriter',
        severity: ChangeSeverity.HIGH,
        category: ChangeCategory.USER_ACTION,
        metadata: {
          source: 'Underwriting System',
          workflowStep: 'Final Approval'
        }
      },
      {
        id: '2',
        entityId: filter.entityId || 'loan-123',
        entityType: filter.entityType || EntityType.LOAN,
        changeType: ChangeType.DOCUMENT_APPROVED,
        description: 'Income verification document approved',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        userId: 'user2',
        userName: 'Sarah Johnson',
        userRole: 'Document Reviewer',
        severity: ChangeSeverity.MEDIUM,
        category: ChangeCategory.USER_ACTION,
        metadata: {
          source: 'Document Management System'
        }
      },
      {
        id: '3',
        entityId: filter.entityId || 'loan-123',
        entityType: filter.entityType || EntityType.LOAN,
        changeType: ChangeType.UPDATED,
        fieldName: 'loanAmount',
        oldValue: 450000,
        newValue: 475000,
        description: 'Loan amount updated',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
        userId: 'user3',
        userName: 'Mike Davis',
        userRole: 'Loan Officer',
        severity: ChangeSeverity.MEDIUM,
        category: ChangeCategory.USER_ACTION
      },
      {
        id: '4',
        entityId: filter.entityId || 'loan-123',
        entityType: filter.entityType || EntityType.LOAN,
        changeType: ChangeType.CREATED,
        description: 'Loan application created',
        timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        userId: 'user4',
        userName: 'Lisa Wilson',
        userRole: 'Loan Officer',
        severity: ChangeSeverity.LOW,
        category: ChangeCategory.USER_ACTION
      }
    ];

    // Apply filters
    let filteredEntries = mockEntries;
    
    if (filter.changeType?.length) {
      filteredEntries = filteredEntries.filter(entry => 
        filter.changeType!.includes(entry.changeType)
      );
    }
    
    if (filter.severity?.length) {
      filteredEntries = filteredEntries.filter(entry => 
        filter.severity!.includes(entry.severity)
      );
    }
    
    if (filter.category?.length) {
      filteredEntries = filteredEntries.filter(entry => 
        filter.category!.includes(entry.category)
      );
    }
    
    if (filter.userId) {
      filteredEntries = filteredEntries.filter(entry => 
        entry.userId.toLowerCase().includes(filter.userId!.toLowerCase()) ||
        entry.userName.toLowerCase().includes(filter.userId!.toLowerCase())
      );
    }
    
    if (filter.fieldName) {
      filteredEntries = filteredEntries.filter(entry => 
        entry.fieldName?.toLowerCase().includes(filter.fieldName!.toLowerCase())
      );
    }
    
    if (filter.dateFrom) {
      filteredEntries = filteredEntries.filter(entry => 
        entry.timestamp >= filter.dateFrom!
      );
    }
    
    if (filter.dateTo) {
      filteredEntries = filteredEntries.filter(entry => 
        entry.timestamp <= filter.dateTo!
      );
    }

    // Apply pagination
    const offset = filter.offset || 0;
    const limit = filter.limit || 50;
    const paginatedEntries = filteredEntries.slice(offset, offset + limit);
    
    return {
      entries: paginatedEntries,
      totalCount: filteredEntries.length,
      hasMore: offset + limit < filteredEntries.length,
      nextOffset: offset + limit < filteredEntries.length ? offset + limit : undefined
    };
  }

  private generateMockCsv(filter: ChangeHistoryFilter): Blob {
    const response = this.generateMockResponse(filter);
    const headers = ['ID', 'Timestamp', 'Change Type', 'Description', 'User', 'Severity'];
    const csvContent = [
      headers.join(','),
      ...response.entries.map(entry => [
        entry.id,
        entry.timestamp.toISOString(),
        entry.changeType,
        `"${entry.description}"`,
        entry.userName,
        entry.severity
      ].join(','))
    ].join('\n');
    
    return new Blob([csvContent], { type: 'text/csv' });
  }

  private generateMockStats(): any {
    return {
      totalChanges: 156,
      changesThisWeek: 23,
      changesThisMonth: 89,
      mostActiveUser: 'John Smith',
      mostCommonChangeType: 'STATUS_CHANGED',
      averageChangesPerDay: 3.2
    };
  }
}
