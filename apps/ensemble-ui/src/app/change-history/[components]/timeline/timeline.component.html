<div class="timeline-container">
  <!-- Loading State -->
  <div *ngIf="loading" class="timeline-loading">
    <mat-card class="rkt-Card rkt-Card--short">
      <div class="loading-skeleton">
        <div class="skeleton-line skeleton-line--title"></div>
        <div class="skeleton-line skeleton-line--subtitle"></div>
        <div class="skeleton-line skeleton-line--content"></div>
      </div>
    </mat-card>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && entries.length === 0" class="timeline-empty">
    <mat-card class="rkt-Card rkt-Card--tall">
      <div class="empty-state">
        <mat-icon class="empty-icon">history</mat-icon>
        <h3 class="rkt-Card__heading rkt-Spacing--mb8">No Change History</h3>
        <p class="rkt-Card__content">
          No changes have been recorded for this loan yet. 
          Changes will appear here as they occur.
        </p>
      </div>
    </mat-card>
  </div>

  <!-- Timeline Content -->
  <div *ngIf="!loading && entries.length > 0" class="timeline">
    <div *ngFor="let group of groupedEntries; trackBy: trackByDate" class="timeline-group">
      
      <!-- Date Header -->
      <div *ngIf="showGrouping" class="timeline-date-header">
        <h4 class="rkt-Card__subtitle">{{ group.date }}</h4>
      </div>

      <!-- Timeline Entries -->
      <div class="timeline-entries">
        <div 
          *ngFor="let entry of group.entries; trackBy: trackByEntry" 
          class="timeline-entry"
          [ngClass]="getSeverityClass(entry)"
        >
          <!-- Timeline Line -->
          <div class="timeline-line">
            <div class="timeline-dot" [ngClass]="'timeline-dot--' + getChangeTypeColor(entry)">
              <mat-icon [matTooltip]="entry.changeType">{{ getChangeTypeIcon(entry) }}</mat-icon>
            </div>
          </div>

          <!-- Timeline Content -->
          <div class="timeline-content">
            <mat-card class="rkt-Card rkt-Card--tiny timeline-card">
              
              <!-- Header with timestamp and severity -->
              <div class="timeline-header">
                <div class="timeline-meta">
                  <span class="timeline-time">{{ formatTimestamp(entry.timestamp) }}</span>
                  <mat-chip 
                    class="severity-chip"
                    [ngClass]="'severity-chip--' + entry.severity.toLowerCase()"
                  >
                    {{ entry.severity }}
                  </mat-chip>
                </div>
                <mat-chip 
                  class="change-type-chip"
                  [ngClass]="'change-type-chip--' + getChangeTypeColor(entry)"
                >
                  {{ formatChangeType(entry.changeType) }}
                </mat-chip>
              </div>

              <!-- Main Content -->
              <div class="timeline-body">
                <h4 class="rkt-Card__heading timeline-title">{{ entry.description }}</h4>
                
                <!-- Field Change Details -->
                <div *ngIf="entry.fieldName" class="field-change">
                  <span class="field-name">{{ entry.fieldName }}:</span>
                  <span class="field-value">{{ formatValueChange(entry) }}</span>
                </div>

                <!-- User Information -->
                <div class="user-info">
                  <mat-icon class="user-icon">person</mat-icon>
                  <span class="user-name">{{ entry.userName }}</span>
                  <span class="user-role">({{ entry.userRole }})</span>
                </div>

                <!-- Additional Metadata -->
                <div *ngIf="entry.metadata" class="metadata">
                  <div *ngIf="entry.metadata.source" class="metadata-item">
                    <mat-icon class="metadata-icon">source</mat-icon>
                    <span>{{ entry.metadata.source }}</span>
                  </div>
                  <div *ngIf="entry.metadata.workflowStep" class="metadata-item">
                    <mat-icon class="metadata-icon">workflow</mat-icon>
                    <span>{{ entry.metadata.workflowStep }}</span>
                  </div>
                </div>
              </div>
            </mat-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
