import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ChangeHistoryEntry, TimelineGroup } from '@rocket-logic-ensemble/models';

@Component({
  selector: 'ens-timeline',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatChipsModule,
    MatTooltipModule
  ],
  templateUrl: './timeline.component.html',
  styleUrl: './timeline.component.scss'
})
export class TimelineComponent implements OnInit {
  @Input() entries: ChangeHistoryEntry[] = [];
  @Input() loading = false;
  @Input() showGrouping = true;

  groupedEntries: TimelineGroup[] = [];

  ngOnInit() {
    this.groupEntries();
  }

  ngOnChanges() {
    this.groupEntries();
  }

  private groupEntries() {
    if (!this.showGrouping) {
      this.groupedEntries = [{
        date: '',
        entries: this.entries
      }];
      return;
    }

    const groups = new Map<string, ChangeHistoryEntry[]>();
    
    this.entries.forEach(entry => {
      const dateKey = this.formatDateKey(entry.timestamp);
      if (!groups.has(dateKey)) {
        groups.set(dateKey, []);
      }
      groups.get(dateKey)!.push(entry);
    });

    this.groupedEntries = Array.from(groups.entries()).map(([date, entries]) => ({
      date,
      entries: entries.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    })).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  private formatDateKey(date: Date): string {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const entryDate = new Date(date);
    entryDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);
    yesterday.setHours(0, 0, 0, 0);

    if (entryDate.getTime() === today.getTime()) {
      return 'Today';
    } else if (entryDate.getTime() === yesterday.getTime()) {
      return 'Yesterday';
    } else {
      return entryDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  }

  getChangeTypeColor(entry: ChangeHistoryEntry): string {
    switch (entry.changeType) {
      case 'CREATED': return 'success';
      case 'UPDATED': return 'primary';
      case 'DELETED': return 'warn';
      case 'STATUS_CHANGED': return 'accent';
      case 'APPROVED': return 'success';
      case 'DENIED': return 'warn';
      case 'DOCUMENT_UPLOADED': return 'primary';
      case 'DOCUMENT_APPROVED': return 'success';
      case 'DOCUMENT_REJECTED': return 'warn';
      default: return 'primary';
    }
  }

  getSeverityClass(entry: ChangeHistoryEntry): string {
    switch (entry.severity) {
      case 'LOW': return 'timeline-entry--low';
      case 'MEDIUM': return 'timeline-entry--medium';
      case 'HIGH': return 'timeline-entry--high';
      case 'CRITICAL': return 'timeline-entry--critical';
      default: return 'timeline-entry--medium';
    }
  }

  formatTimestamp(timestamp: Date): string {
    return timestamp.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }

  formatValueChange(entry: ChangeHistoryEntry): string {
    if (!entry.oldValue && !entry.newValue) return '';
    
    const oldVal = this.formatValue(entry.oldValue);
    const newVal = this.formatValue(entry.newValue);
    
    if (!entry.oldValue) return `Set to: ${newVal}`;
    if (!entry.newValue) return `Removed: ${oldVal}`;
    
    return `${oldVal} → ${newVal}`;
  }

  private formatValue(value: any): string {
    if (value === null || value === undefined) return 'None';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    if (value instanceof Date) return value.toLocaleDateString();
    return String(value);
  }

  getChangeTypeIcon(entry: ChangeHistoryEntry): string {
    switch (entry.changeType) {
      case 'CREATED': return 'add_circle';
      case 'UPDATED': return 'edit';
      case 'DELETED': return 'delete';
      case 'STATUS_CHANGED': return 'swap_horiz';
      case 'DOCUMENT_UPLOADED': return 'upload_file';
      case 'DOCUMENT_APPROVED': return 'check_circle';
      case 'DOCUMENT_REJECTED': return 'cancel';
      case 'COMMENT_ADDED': return 'comment';
      case 'ASSIGNED': return 'person_add';
      case 'UNASSIGNED': return 'person_remove';
      case 'APPROVED': return 'thumb_up';
      case 'DENIED': return 'thumb_down';
      case 'CONDITIONS_ADDED': return 'warning';
      case 'CONDITIONS_CLEARED': return 'check';
      default: return 'info';
    }
  }

  trackByDate(_index: number, group: TimelineGroup): string {
    return group.date;
  }

  trackByEntry(_index: number, entry: ChangeHistoryEntry): string {
    return entry.id;
  }

  formatChangeType(changeType: string): string {
    return changeType.replace(/_/g, ' ');
  }
}
