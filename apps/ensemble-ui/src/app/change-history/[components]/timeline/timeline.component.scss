.timeline-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

// Loading State
.timeline-loading {
  .loading-skeleton {
    .skeleton-line {
      height: 16px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: 4px;
      margin-bottom: 8px;

      &--title {
        height: 20px;
        width: 60%;
      }

      &--subtitle {
        height: 16px;
        width: 40%;
      }

      &--content {
        height: 14px;
        width: 80%;
      }
    }
  }
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Empty State
.timeline-empty {
  .empty-state {
    text-align: center;
    padding: 2rem;

    .empty-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #9e9e9e;
      margin-bottom: 1rem;
    }
  }
}

// Timeline
.timeline {
  position: relative;
}

.timeline-group {
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.timeline-date-header {
  margin-bottom: 1rem;
  padding-left: 3rem;

  h4 {
    color: #666;
    font-weight: 600;
  }
}

.timeline-entries {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 1.5rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e0e0e0;
    z-index: 1;
  }
}

.timeline-entry {
  position: relative;
  display: flex;
  margin-bottom: 1.5rem;

  &:last-child {
    margin-bottom: 0;
  }

  // Severity styling
  &--low {
    .timeline-dot {
      border-color: #4caf50;
    }
  }

  &--medium {
    .timeline-dot {
      border-color: #ff9800;
    }
  }

  &--high {
    .timeline-dot {
      border-color: #f44336;
    }
  }

  &--critical {
    .timeline-dot {
      border-color: #d32f2f;
      box-shadow: 0 0 0 4px rgba(211, 47, 47, 0.2);
    }
  }
}

.timeline-line {
  position: relative;
  width: 3rem;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
}

.timeline-dot {
  position: relative;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: white;
  border: 3px solid #2196f3;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  mat-icon {
    font-size: 1.2rem;
    width: 1.2rem;
    height: 1.2rem;
    color: #666;
  }

  &--success {
    border-color: #4caf50;
    mat-icon {
      color: #4caf50;
    }
  }

  &--primary {
    border-color: #2196f3;
    mat-icon {
      color: #2196f3;
    }
  }

  &--warn {
    border-color: #f44336;
    mat-icon {
      color: #f44336;
    }
  }

  &--accent {
    border-color: #ff9800;
    mat-icon {
      color: #ff9800;
    }
  }
}

.timeline-content {
  flex: 1;
  margin-left: 1rem;
}

.timeline-card {
  .timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .timeline-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .timeline-time {
    font-size: 0.875rem;
    color: #666;
    font-weight: 500;
  }

  .severity-chip {
    font-size: 0.75rem;
    height: 1.5rem;
    
    &--low {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    &--medium {
      background-color: #fff3e0;
      color: #ef6c00;
    }

    &--high {
      background-color: #ffebee;
      color: #c62828;
    }

    &--critical {
      background-color: #ffebee;
      color: #b71c1c;
      font-weight: 600;
    }
  }

  .change-type-chip {
    font-size: 0.75rem;
    height: 1.5rem;
    text-transform: capitalize;

    &--success {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    &--primary {
      background-color: #e3f2fd;
      color: #1565c0;
    }

    &--warn {
      background-color: #ffebee;
      color: #c62828;
    }

    &--accent {
      background-color: #fff3e0;
      color: #ef6c00;
    }
  }

  .timeline-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
  }

  .field-change {
    background: #f5f5f5;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;

    .field-name {
      font-weight: 600;
      color: #333;
    }

    .field-value {
      color: #666;
      margin-left: 0.5rem;
    }
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    color: #666;
    margin-bottom: 0.5rem;

    .user-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }

    .user-name {
      font-weight: 500;
      color: #333;
    }

    .user-role {
      font-style: italic;
    }
  }

  .metadata {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;

    .metadata-item {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      font-size: 0.75rem;
      color: #888;

      .metadata-icon {
        font-size: 0.875rem;
        width: 0.875rem;
        height: 0.875rem;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .timeline-date-header {
    padding-left: 2rem;
  }

  .timeline-line {
    width: 2rem;
  }

  .timeline-dot {
    width: 2rem;
    height: 2rem;

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }
  }

  .timeline-content {
    margin-left: 0.5rem;
  }

  .timeline-header {
    flex-direction: column;
    align-items: flex-start !important;
  }
}
