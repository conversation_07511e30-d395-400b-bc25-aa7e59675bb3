<div class="change-history-container">
  <!-- Header -->
  <div class="change-history-header">
    <mat-card class="rkt-Card rkt-Card--short">
      <div class="header-content">
        <div class="header-info">
          <h2 class="rkt-Card__heading">Change History</h2>
          <p class="rkt-Card__content" *ngIf="totalCount > 0">
            {{ totalCount }} change{{ totalCount !== 1 ? 's' : '' }} recorded
          </p>
        </div>
        
        <div class="header-actions">
          <button 
            mat-icon-button 
            class="rkt-Button rkt-Button--icon"
            (click)="refreshChangeHistory()"
            [disabled]="loading"
            matTooltip="Refresh"
          >
            <mat-icon>refresh</mat-icon>
          </button>
          
          <button 
            mat-stroked-button 
            class="rkt-Button rkt-Button--secondary"
            (click)="exportHistory()"
            [disabled]="loading || entries.length === 0"
          >
            <mat-icon>download</mat-icon>
            Export
          </button>
        </div>
      </div>
    </mat-card>
  </div>

  <!-- Filters -->
  <div *ngIf="showFilters" class="change-history-filters">
    <mat-card class="rkt-Card rkt-Card--short">
      <form [formGroup]="filterForm" class="filter-form">
        <div class="filter-row">
          <!-- Change Type Filter -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Change Type</mat-label>
            <mat-select formControlName="changeType" multiple>
              <mat-option *ngFor="let type of changeTypes" [value]="type">
                {{ formatEnumValue(type) }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Severity Filter -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Severity</mat-label>
            <mat-select formControlName="severity" multiple>
              <mat-option *ngFor="let severity of severities" [value]="severity">
                {{ severity }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Category Filter -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Category</mat-label>
            <mat-select formControlName="category" multiple>
              <mat-option *ngFor="let category of categories" [value]="category">
                {{ formatEnumValue(category) }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="filter-row">
          <!-- Date From -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>From Date</mat-label>
            <input matInput [matDatepicker]="fromPicker" formControlName="dateFrom">
            <mat-datepicker-toggle matIconSuffix [for]="fromPicker"></mat-datepicker-toggle>
            <mat-datepicker #fromPicker></mat-datepicker>
          </mat-form-field>

          <!-- Date To -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>To Date</mat-label>
            <input matInput [matDatepicker]="toPicker" formControlName="dateTo">
            <mat-datepicker-toggle matIconSuffix [for]="toPicker"></mat-datepicker-toggle>
            <mat-datepicker #toPicker></mat-datepicker>
          </mat-form-field>

          <!-- User Filter -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>User</mat-label>
            <input matInput formControlName="userId" placeholder="Enter user name or ID">
          </mat-form-field>
        </div>

        <div class="filter-row">
          <!-- Field Name Filter -->
          <mat-form-field appearance="outline" class="filter-field">
            <mat-label>Field Name</mat-label>
            <input matInput formControlName="fieldName" placeholder="Enter field name">
          </mat-form-field>

          <!-- Clear Filters Button -->
          <div class="filter-actions">
            <button 
              type="button"
              mat-stroked-button 
              class="rkt-Button rkt-Button--secondary"
              (click)="clearFilters()"
            >
              <mat-icon>clear</mat-icon>
              Clear Filters
            </button>
          </div>
        </div>
      </form>
    </mat-card>
  </div>

  <!-- Timeline -->
  <div class="change-history-timeline">
    <ens-timeline
      [entries]="entries"
      [loading]="loading"
      [showGrouping]="true"
    ></ens-timeline>

    <!-- Load More Button -->
    <div *ngIf="hasMore" class="load-more-container">
      <button 
        mat-stroked-button 
        class="rkt-Button rkt-Button--large rkt-Button--secondary"
        (click)="loadMore()"
        [disabled]="loading"
      >
        <mat-spinner *ngIf="loading" diameter="20" class="load-more-spinner"></mat-spinner>
        <mat-icon *ngIf="!loading">expand_more</mat-icon>
        {{ loading ? 'Loading...' : 'Load More' }}
      </button>
    </div>

    <!-- No More Results -->
    <div *ngIf="!hasMore && entries.length > 0" class="no-more-results">
      <p class="rkt-Body-16">You've reached the end of the change history.</p>
    </div>
  </div>
</div>
