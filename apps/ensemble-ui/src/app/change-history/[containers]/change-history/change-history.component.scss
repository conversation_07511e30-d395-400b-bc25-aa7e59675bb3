.change-history-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

// Header
.change-history-header {
  margin-bottom: 1.5rem;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-info {
    flex: 1;

    h2 {
      margin-bottom: 0.5rem;
    }

    p {
      margin: 0;
      color: #666;
    }
  }

  .header-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
  }
}

// Filters
.change-history-filters {
  margin-bottom: 1.5rem;

  .filter-form {
    .filter-row {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
      flex-wrap: wrap;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .filter-field {
      flex: 1;
      min-width: 200px;
    }

    .filter-actions {
      display: flex;
      align-items: flex-end;
      padding-bottom: 1.25rem; // Align with form fields
    }
  }
}

// Timeline
.change-history-timeline {
  .load-more-container {
    display: flex;
    justify-content: center;
    margin-top: 2rem;

    .load-more-spinner {
      margin-right: 0.5rem;
    }
  }

  .no-more-results {
    text-align: center;
    margin-top: 2rem;
    padding: 1rem;
    color: #666;
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .change-history-container {
    padding: 0.75rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch !important;
  }

  .header-actions {
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .change-history-container {
    padding: 0.5rem;
  }

  .filter-row {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .filter-field {
    min-width: unset !important;
  }

  .filter-actions {
    padding-bottom: 0 !important;
    margin-top: 0.5rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.5rem !important;
    align-items: stretch;

    button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .change-history-container {
    padding: 0.25rem;
  }

  .change-history-header,
  .change-history-filters {
    margin-bottom: 1rem;
  }
}

// Loading States
.filter-form {
  &.loading {
    opacity: 0.6;
    pointer-events: none;
  }
}

// Animation for smooth transitions
.change-history-timeline {
  transition: opacity 0.3s ease;

  &.loading {
    opacity: 0.8;
  }
}

// Custom styling for Material components
::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      background-color: white;
    }
  }

  .mat-mdc-select-panel {
    max-height: 300px;
  }

  .mat-mdc-chip-set {
    .mat-mdc-chip {
      margin: 2px;
    }
  }
}

// Accessibility improvements
.change-history-container {
  [role="button"] {
    cursor: pointer;
  }

  .filter-field {
    &:focus-within {
      .mat-mdc-form-field-outline {
        border-color: #2196f3;
        border-width: 2px;
      }
    }
  }
}

// Print styles
@media print {
  .change-history-header .header-actions,
  .change-history-filters,
  .load-more-container {
    display: none !important;
  }

  .change-history-container {
    max-width: none;
    padding: 0;
  }
}
