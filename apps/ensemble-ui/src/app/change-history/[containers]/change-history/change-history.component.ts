import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

import { TimelineComponent } from '../../[components]/timeline/timeline.component';
import { ChangeHistoryService } from '../../[services]/change-history.service';
import {
  ChangeHistoryEntry,
  ChangeHistoryFilter,
  EntityType,
  ChangeType,
  ChangeSeverity,
  ChangeCategory
} from '@rocket-logic-ensemble/models';

@Component({
  selector: 'ens-change-history',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatInputModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    TimelineComponent
  ],
  templateUrl: './change-history.component.html',
  styleUrl: './change-history.component.scss'
})
export class ChangeHistoryComponent implements OnInit, OnDestroy {
  @Input() entityId!: string;
  @Input() entityType: EntityType = EntityType.LOAN;
  @Input() showFilters = true;
  @Input() autoRefresh = false;
  @Input() refreshInterval = 30000; // 30 seconds

  filterForm: FormGroup;
  entries: ChangeHistoryEntry[] = [];
  loading = false;
  hasMore = false;
  totalCount = 0;
  
  // Filter options
  changeTypes = Object.values(ChangeType);
  severities = Object.values(ChangeSeverity);
  categories = Object.values(ChangeCategory);
  
  private destroy$ = new Subject<void>();
  private refreshTimer?: number;

  constructor(
    private fb: FormBuilder,
    private changeHistoryService: ChangeHistoryService
  ) {
    this.filterForm = this.createFilterForm();
  }

  ngOnInit() {
    this.setupFormSubscription();
    this.loadChangeHistory();
    
    if (this.autoRefresh) {
      this.startAutoRefresh();
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.stopAutoRefresh();
  }

  private createFilterForm(): FormGroup {
    return this.fb.group({
      changeType: [[]],
      severity: [[]],
      category: [[]],
      dateFrom: [null],
      dateTo: [null],
      userId: [''],
      fieldName: ['']
    });
  }

  private setupFormSubscription() {
    this.filterForm.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.loadChangeHistory();
      });
  }

  private startAutoRefresh() {
    this.refreshTimer = window.setInterval(() => {
      this.refreshChangeHistory();
    }, this.refreshInterval);
  }

  private stopAutoRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = undefined;
    }
  }

  loadChangeHistory(append = false) {
    if (this.loading) return;

    this.loading = true;

    const filter = this.buildFilter(append);

    this.changeHistoryService.getChangeHistory(filter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (append) {
            this.entries = [...this.entries, ...response.entries];
          } else {
            this.entries = response.entries;
          }

          this.hasMore = response.hasMore;
          this.totalCount = response.totalCount;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading change history:', error);
          this.loading = false;
        }
      });
  }

  refreshChangeHistory() {
    this.loadChangeHistory(false);
  }

  loadMore() {
    if (this.hasMore && !this.loading) {
      this.loadChangeHistory(true);
    }
  }

  clearFilters() {
    this.filterForm.reset({
      changeType: [],
      severity: [],
      category: [],
      dateFrom: null,
      dateTo: null,
      userId: '',
      fieldName: ''
    });
  }

  exportHistory() {
    const filter = this.buildFilter(false);

    this.changeHistoryService.exportChangeHistory(filter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (blob) => {
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `change-history-${this.entityId}-${new Date().toISOString().split('T')[0]}.csv`;
          link.click();
          window.URL.revokeObjectURL(url);
        },
        error: (error) => {
          console.error('Error exporting change history:', error);
        }
      });
  }

  private buildFilter(append: boolean): ChangeHistoryFilter {
    const formValue = this.filterForm.value;
    
    return {
      entityId: this.entityId,
      entityType: this.entityType,
      changeType: formValue.changeType?.length ? formValue.changeType : undefined,
      severity: formValue.severity?.length ? formValue.severity : undefined,
      category: formValue.category?.length ? formValue.category : undefined,
      dateFrom: formValue.dateFrom,
      dateTo: formValue.dateTo,
      userId: formValue.userId || undefined,
      fieldName: formValue.fieldName || undefined,
      limit: 20,
      offset: append ? this.entries.length : 0
    };
  }

  formatEnumValue(value: string): string {
    return value.replace(/_/g, ' ');
  }
}
