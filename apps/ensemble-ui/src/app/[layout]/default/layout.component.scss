.layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background-color: #f8f9fa;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

// Minimal Header
.layout-header {
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

// Project Branding
.project-branding {
  .project-name {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1976d2;
    letter-spacing: -0.025em;
  }
}

// Header Actions (for future projection)
.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
  margin-right: 1rem;
}

// Account Menu
.account-menu {
  position: relative;
}

.account-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.user-name {
  font-weight: 500;
  font-size: 0.875rem;
  color: #333;
  line-height: 1.2;
}

// Account Dropdown Menu
::ng-deep .account-dropdown {
  .mat-mdc-menu-panel {
    min-width: 280px;
    max-width: 320px;
  }
}

.account-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.user-avatar-large {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.125rem;
}

.user-details {
  flex: 1;
}

.user-name-large {
  font-weight: 600;
  font-size: 1rem;
  color: #333;
  margin-bottom: 0.25rem;
}

.user-email {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.user-role-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

// Side Menu Overlay (for future projection)
.side-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;

  &.open {
    pointer-events: all;
    opacity: 1;
  }
}

.side-menu-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}

.side-menu-panel {
  position: absolute;
  top: 0;
  left: 0;
  width: 320px;
  height: 100%;
  background-color: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transform: translateX(-100%);
  transition: transform 0.3s ease;

  .side-menu-overlay.open & {
    transform: translateX(0);
  }
}

// Bottom Sheet Overlay (for future projection)
.bottom-sheet-overlay {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;

  &.open {
    pointer-events: all;
    opacity: 1;
  }
}

.bottom-sheet-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}

.bottom-sheet-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  max-height: 80%;
  background-color: white;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(100%);
  transition: transform 0.3s ease;

  .bottom-sheet-overlay.open & {
    transform: translateY(0);
  }
}

// Responsive Main Content
.layout-main {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  min-height: 0; // Important for flex overflow
}

.main-content-area {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; // Important for flex children to respect overflow
}

// Contextual Panels (for future projection)
.contextual-panels {
  display: flex;
  flex-direction: column;
  width: 0;
  overflow: hidden;
  transition: width 0.3s ease;
  background-color: white;
  border-left: 1px solid #e0e0e0;

  &:has(*) {
    width: 320px;
    padding: 1rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .header-content {
    padding: 0.75rem 1rem;
  }

  .user-info {
    display: none;
  }

  .project-branding .project-name {
    font-size: 1.25rem;
  }

  .side-menu-panel {
    width: 280px;
  }

  .contextual-panels {
    &:has(*) {
      width: 100%;
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      z-index: 100;
      box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
    }
  }

  .layout-main {
    flex-direction: column;
  }
}
