<div class="app-container">
  <!-- Left Menu - Full viewport height -->
  <nav class="sidebar" [class.visible]="sideMenuOpen">
    <div class="sidebar-content">
      <div class="nav-item" (click)="navigateToLoanSearch(); closeSideMenu()">
        <mat-icon>search</mat-icon>
        <span>Loan Search</span>
      </div>

      <div class="nav-item" (click)="navigateToLoanStatus(); closeSideMenu()">
        <mat-icon>assignment</mat-icon>
        <span>Loan Status</span>
      </div>

      <div class="nav-item" (click)="navigateToLoanTrace(); closeSideMenu()">
        <mat-icon>timeline</mat-icon>
        <span>Loan Trace</span>
      </div>

      <div class="nav-item" (click)="navigateToLogs(); closeSideMenu()">
        <mat-icon>description</mat-icon>
        <span>Logs</span>
      </div>
    </div>
  </nav>

  <!-- Main Content with Header Component -->
  <main class="main-content" [class.shifted]="sideMenuOpen">
    <!-- Header Component at top of content -->
    <header class="content-header">
      <button
        mat-icon-button
        class="menu-btn"
        (click)="sideMenuOpen ? closeSideMenu() : openSideMenu()"
      >
        <mat-icon>menu</mat-icon>
      </button>

      <div class="loan-display">
        Loan: {{ activeLoanNumber }}
      </div>

      <div class="user-menu">
        <button
          mat-icon-button
          [matMenuTriggerFor]="accountMenu"
          class="user-btn"
        >
          <div class="user-avatar">{{ user.avatar }}</div>
        </button>

        <mat-menu #accountMenu="matMenu">
          <button mat-menu-item (click)="openProfile()">
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>
          <button mat-menu-item (click)="openSettings()">
            <mat-icon>settings</mat-icon>
            <span>Settings</span>
          </button>
          <mat-divider></mat-divider>
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Sign Out</span>
          </button>
        </mat-menu>
      </div>
    </header>

    <!-- Page Content -->
    <div class="page-content">
      <ng-content></ng-content>
    </div>
  </main>
</div>
