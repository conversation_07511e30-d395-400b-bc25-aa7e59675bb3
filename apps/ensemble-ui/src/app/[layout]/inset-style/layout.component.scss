// Google-style simple layout
.app-container {
  height: 100vh;
  width: 100vw;
  background: #f8f9fa;
  font-family: 'Google Sans', 'Roboto', sans-serif;
  position: relative;
  overflow: hidden;
}

// Left Sidebar - Full viewport height, behind main content
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background: #f8f9fa;
  z-index: 1; // Behind main content
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;

  &.visible {
    opacity: 1;
    visibility: visible;
  }
}

.sidebar-content {
  padding: 24px 0; // No top padding needed since header is in content
  transform: translateX(-20px);
  transition: transform 0.3s ease 0.1s; // Slight delay for stagger effect

  .sidebar.visible & {
    transform: translateX(0);
  }
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 24px;
  cursor: pointer;
  transition: background-color 0.2s ease, opacity 0.3s ease, transform 0.3s ease;
  color: #5f6368;
  opacity: 0;
  transform: translateX(-10px);

  &:hover {
    background-color: rgba(60, 64, 67, 0.08);
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }

  span {
    font-size: 14px;
    font-weight: 400;
  }

  // Staggered animation for each nav item
  .sidebar.visible & {
    opacity: 1;
    transform: translateX(0);

    &:nth-child(1) { transition-delay: 0.1s; }
    &:nth-child(2) { transition-delay: 0.15s; }
    &:nth-child(3) { transition-delay: 0.2s; }
    &:nth-child(4) { transition-delay: 0.25s; }
    &:nth-child(5) { transition-delay: 0.3s; }
    &:nth-child(6) { transition-delay: 0.35s; }
  }
}

// Main Content - Slides right when menu opens, appears above sidebar
.main-content {
  height: 100vh;
  width: 100vw;
  background: #ffffff;
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),
              border-radius 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),
              box-shadow 0.4s ease;
  position: relative;
  z-index: 2; // Above sidebar
  border-radius: 0 0 0 0; // Rounded top-left corner
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); // Subtle shadow
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &.shifted {
    transform: translateX(280px);
    border-radius: 35px 0 0 35px; // Rounded left corners when shifted
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.15), 0 0 20px rgba(0, 0, 0, 0.1);
  }
}

// Header Component inside content
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: #ffffff;
  border-bottom: 1px solid #e8eaed;
  flex-shrink: 0; // Don't shrink
}

.menu-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(60, 64, 67, 0.08);
  }

  mat-icon {
    color: #5f6368;
    font-size: 24px;
  }
}

.loan-display {
  font-size: 16px;
  color: #202124;
  font-weight: 400;
}

.user-menu {
  .user-btn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(60, 64, 67, 0.08);
    }
  }

  .user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #1a73e8;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
  }
}

// Page Content Area
.page-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
}

// Responsive Design
@media (max-width: 768px) {
  .sidebar {
    width: 240px;
  }

  .main-content.shifted {
    transform: translateX(240px);
  }

  .header {
    padding: 12px 16px;
  }

  .loan-display {
    font-size: 14px;
  }
}
