# Layout

This directory contains components responsible for the overall layout and structure of the application.

## Overview

The layout components define the visual framework of the application, including:

- Main application shell
- Navigation elements
- Responsive layout containers
- Common UI patterns that appear across multiple pages

These components are typically not tied to specific business logic but focus on providing a consistent user experience and visual structure.

## Key Components

### Layout Component

The root layout component that wraps the entire application.

**Features:**
- Main container for the application
- Handles responsive behavior
- Provides consistent padding and spacing

### Shell Component

The main application shell that includes navigation and common UI elements.

**Features:**
- Top navigation bar
- Sidebar navigation
- User profile menu
- Application branding
- Responsive behavior for different screen sizes

### Navigation Components

Components for navigating through the application.

**Components:**
- **TopNav**: Primary navigation in the header
- **SideNav**: Secondary navigation in the sidebar
- **BreadcrumbNav**: Contextual navigation showing current location
- **TabNav**: Tab-based navigation within features

## Layout Structure

The application follows a common layout pattern:

```
<layout>
  <shell>
    <top-nav>...</top-nav>
    <side-nav>...</side-nav>
    <main>
      <router-outlet></router-outlet>
    </main>
    <footer>...</footer>
  </shell>
</layout>
```

## Theming

The layout components use Angular Material with a custom theme:

- Primary color: #1976d2 (blue)
- Secondary color: #3f51b5 (indigo)
- Accent color: #ff4081 (pink)
- Background: Light theme with subtle gradients
- Typography: Roboto font family

## Responsive Design

The layout is designed to work across different screen sizes:

- **Desktop**: Full navigation with expanded sidebar
- **Tablet**: Collapsible sidebar, simplified navigation
- **Mobile**: Bottom navigation, hamburger menu

## Development Guidelines

When working with layout components:

1. Focus on responsive behavior
2. Maintain consistent spacing and alignment
3. Use Angular Material components
4. Keep layout components free of business logic
5. Use CSS Grid and Flexbox for layout
6. Follow accessibility best practices
7. Test across different screen sizes