import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { LayoutComponent } from '../../[layout]/default/layout.component';
import { InsetLayoutComponent } from '../../[layout]/inset-style/layout.component';

@Component({
  selector: 'ens-layout-demo',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    LayoutComponent,
    InsetLayoutComponent
  ],
  templateUrl: './layout-demo.component.html',
  styleUrl: './layout-demo.component.scss'
})
export class LayoutDemoComponent {
  currentLayout: 'default' | 'inset' = 'inset';

  switchToDefault() {
    this.currentLayout = 'default';
  }

  switchToInset() {
    this.currentLayout = 'inset';
  }
}
