<div class="layout-demo-container">
  <!-- Layout Switcher -->
  <div class="layout-switcher">
    <mat-card class="switcher-card">
      <div class="switcher-content">
        <h2>Layout Demo</h2>
        <p>Switch between different layout styles to compare designs:</p>
        
        <div class="layout-buttons">
          <button 
            mat-raised-button 
            [color]="currentLayout === 'default' ? 'primary' : ''"
            (click)="switchToDefault()"
          >
            <mat-icon>view_compact</mat-icon>
            Default Layout
          </button>
          
          <button
            mat-raised-button
            [color]="currentLayout === 'inset' ? 'primary' : ''"
            (click)="switchToInset()"
          >
            <mat-icon>desktop_windows</mat-icon>
            Inset Style
          </button>
        </div>
      </div>
    </mat-card>
  </div>

  <!-- Layout Display -->
  <div class="layout-display">
    <!-- Default Layout -->
    <ens-layout *ngIf="currentLayout === 'default'">
      <div class="demo-content">
        <mat-card class="content-card">
          <h3>Default Layout Content</h3>
          <p>This is the original layout design with a clean, minimal header and traditional sidebar navigation.</p>
          
          <div class="feature-list">
            <h4>Features:</h4>
            <ul>
              <li>Clean minimal header</li>
              <li>Traditional Material Design styling</li>
              <li>Account menu with user profile</li>
              <li>Responsive design</li>
              <li>Content projection slots</li>
            </ul>
          </div>
        </mat-card>
      </div>
    </ens-layout>

    <!-- Inset Layout -->
    <ens-inset-layout *ngIf="currentLayout === 'inset'">
      <div class="demo-content">
        <mat-card class="content-card inset-content">
          <h3>Inset-Style Layout Content</h3>
          <p>This layout features distinctive design with rounded edges and inset effects for a modern, dimensional look.</p>
          
          <div class="feature-list">
            <h4>Features:</h4>
            <ul>
              <li>Rounded corners throughout</li>
              <li>Inset shadow effects for depth</li>
              <li>Active loan number display in top bar</li>
              <li>Modern dimensional color scheme</li>
              <li>Gradient backgrounds</li>
              <li>Interactive navigation with hover effects</li>
              <li>Status indicator in footer</li>
            </ul>
          </div>

          <div class="demo-actions">
            <h4>Try the Navigation:</h4>
            <p>Click on the navigation items in the left sidebar to see the hover and active states in action.</p>
          </div>
        </mat-card>
      </div>
    </ens-inset-layout>
  </div>
</div>
