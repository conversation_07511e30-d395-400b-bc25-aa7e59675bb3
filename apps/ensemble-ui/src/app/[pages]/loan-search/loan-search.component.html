<div class="loan-search-container" [class.searching]="isSearching">
  <!-- Minimal Search Form -->
  <div class="search-form">
    <div class="search-input-container">
      <mat-form-field appearance="outline" class="search-input">
        <mat-label>Loan Number</mat-label>
        <input
          matInput
          [formControl]="loanNumberControl"
          placeholder="Enter 10-digit loan number"
          maxlength="12"
          (input)="onInputChange($event)"
          (keydown)="onEnterKey($event)"
          autocomplete="off"
          inputmode="numeric">
        <mat-hint *ngIf="shouldShowRateLimitWarning() && !loanNumberControl.invalid">
          {{ getRemainingAttempts() }} search{{ getRemainingAttempts() === 1 ? '' : 'es' }} remaining
        </mat-hint>
        <mat-error *ngIf="loanNumberControl.invalid && loanNumberControl.touched">
          {{ getErrorMessage() }}
        </mat-error>
      </mat-form-field>

      <button
        mat-raised-button
        color="primary"
        (click)="search()"
        [disabled]="isSearchDisabled()"
        class="search-button">
        <mat-spinner *ngIf="isSearching" diameter="20"></mat-spinner>
        {{ isSearching ? 'Searching...' : 'Search' }}
      </button>

      <button
        *ngIf="loanNumberControl.value && loanNumberControl.value.trim()"
        mat-icon-button
        (click)="clearSearch()"
        [disabled]="isSearching"
        class="clear-button">
        <mat-icon>clear</mat-icon>
      </button>
    </div>

    <!-- Search Error -->
    <div *ngIf="searchError" class="search-error">
      <mat-icon>error</mat-icon>
      <span>{{ searchError }}</span>
    </div>
  </div>

  <!-- Recent Searches -->
  <div *ngIf="recentSearches.length > 0" class="recent-searches">
    <div class="recent-searches-header">
      <mat-icon>history</mat-icon>
      <span>Recent Searches</span>
      <button
        mat-button
        (click)="clearRecentSearches()"
        class="clear-all-button">
        <mat-icon>clear_all</mat-icon>
        Clear All
      </button>
    </div>

    <div class="recent-search-item"
         *ngFor="let loanNumber of recentSearches; trackBy: trackByLoanNumber"
         (click)="selectRecentSearch(loanNumber)">
      <mat-icon>search</mat-icon>
      <span class="loan-number">{{ formatLoanNumber(loanNumber) }}</span>
      <button
        mat-icon-button
        class="remove-button"
        (click)="removeFromRecent(loanNumber, $event)">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Search Tips -->
  <div class="search-tips">
    <mat-icon class="tip-icon">info_outline</mat-icon>
    <p class="tip-text">Enter a 10-digit loan number to search</p>
  </div>
</div>
