<div class="loan-search-container" [class.searching]="isSearching">
  <!-- Centered Unified Search Form -->
  <div class="search-form">
    <div class="search-input-container">
      <div class="search-controls">
        <mat-form-field appearance="outline" class="search-input">
          <mat-label>Loan Number</mat-label>
          <input
            matInput
            [formControl]="loanNumberControl"
            (input)="onInputChange($event)"
            (keydown)="onEnterKey($event)"
            autocomplete="off"
            inputmode="numeric">
        </mat-form-field>

        <button
          mat-icon-button
          (click)="search()"
          [disabled]="isSearching"
          class="search-button">
          <mat-spinner *ngIf="isSearching" diameter="16"></mat-spinner>
          <mat-icon *ngIf="!isSearching">search</mat-icon>
        </button>

        <button
          mat-icon-button
          (click)="clearSearch()"
          [disabled]="isSearching"
          class="clear-button"
          [class.visible]="loanNumberControl.value && loanNumberControl.value.trim()">
          <mat-icon>clear</mat-icon>
        </button>
      </div>
    </div>

    <!-- Search Error -->
    <div *ngIf="searchError" class="search-error">
      <mat-icon>error</mat-icon>
      <span>{{ searchError }}</span>
    </div>
  </div>

  <!-- Recent Searches -->
  <div *ngIf="recentSearches.length > 0" class="recent-searches">
    <div class="recent-searches-header">
      <mat-icon>history</mat-icon>
      <span>Recent Searches</span>
      <button
        mat-button
        (click)="clearRecentSearches()"
        class="clear-all-button">
        <mat-icon>clear_all</mat-icon>
        Clear All
      </button>
    </div>

    <div class="recent-search-item"
         *ngFor="let loanNumber of recentSearches; trackBy: trackByLoanNumber"
         (click)="selectRecentSearch(loanNumber)">
      <mat-icon>search</mat-icon>
      <span class="loan-number">{{ formatLoanNumber(loanNumber) }}</span>
      <button
        mat-icon-button
        class="remove-button"
        (click)="removeFromRecent(loanNumber, $event)">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>
</div>
