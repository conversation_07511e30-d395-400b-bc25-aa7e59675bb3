<div class="loan-search-container" [class.searching]="isSearching">
  <!-- Header -->
  <div class="search-header">
    <h1 class="search-title">Loan Search</h1>
    <p class="search-subtitle">Enter a loan number to view loan details and status</p>
  </div>

  <!-- Search Form -->
  <mat-card class="search-card">
    <mat-card-content>
      <div class="search-form">
        <mat-form-field appearance="outline" class="search-input">
          <mat-label>Loan Number</mat-label>
          <input
            matInput
            [formControl]="loanNumberControl"
            placeholder="Enter 10-digit loan number (e.g., ************)"
            maxlength="12"
            (input)="onInputChange($event)"
            (keydown)="onEnterKey($event)"
            autocomplete="off"
            inputmode="numeric">
          <mat-icon matSuffix>search</mat-icon>
          <mat-hint *ngIf="shouldShowRateLimitWarning() && !loanNumberControl.invalid">
            {{ getRemainingAttempts() }} search{{ getRemainingAttempts() === 1 ? '' : 'es' }} remaining
          </mat-hint>
          <mat-error *ngIf="loanNumberControl.invalid && loanNumberControl.touched">
            {{ getErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <div class="search-actions">
          <button
            mat-raised-button
            color="primary"
            (click)="search()"
            [disabled]="isSearchDisabled()"
            class="search-button">
            <mat-icon *ngIf="!isSearching">search</mat-icon>
            <mat-spinner *ngIf="isSearching" diameter="20"></mat-spinner>
            {{ isSearching ? 'Searching...' : 'Search' }}
          </button>

          <button 
            mat-button
            (click)="clearSearch()"
            [disabled]="isSearching"
            class="clear-button">
            <mat-icon>clear</mat-icon>
            Clear
          </button>
        </div>
      </div>

      <!-- Search Error -->
      <div *ngIf="searchError" class="search-error">
        <mat-icon color="warn">error</mat-icon>
        <span>{{ searchError }}</span>
      </div>

      <!-- Rate Limit Warning -->
      <div *ngIf="searchAttempts >= maxSearchAttempts" class="rate-limit-warning">
        <mat-icon color="warn">warning</mat-icon>
        <span>Search limit reached. Please wait a moment before trying again.</span>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Recent Searches -->
  <mat-card *ngIf="recentSearches.length > 0" class="recent-searches-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>history</mat-icon>
        Recent Searches
      </mat-card-title>
      <div class="header-actions">
        <button 
          mat-icon-button 
          (click)="clearRecentSearches()"
          matTooltip="Clear recent searches">
          <mat-icon>clear_all</mat-icon>
        </button>
      </div>
    </mat-card-header>
    
    <mat-card-content>
      <mat-list>
        <mat-list-item 
          *ngFor="let loanNumber of recentSearches; trackBy: trackByLoanNumber"
          (click)="selectRecentSearch(loanNumber)"
          class="recent-search-item">
          <mat-icon matListItemIcon>description</mat-icon>
          <div matListItemTitle>{{ formatLoanNumber(loanNumber) }}</div>
          <div matListItemLine>Loan Number</div>
          <mat-icon matListItemMeta>arrow_forward</mat-icon>
        </mat-list-item>
      </mat-list>
    </mat-card-content>
  </mat-card>

  <!-- Help Text -->
  <mat-card class="help-card">
    <mat-card-content>
      <div class="help-content">
        <mat-icon color="primary">info</mat-icon>
        <div class="help-text">
          <h3>How to search</h3>
          <ul>
            <li>Enter a 10-digit loan number (e.g., 1234567890)</li>
            <li>Click Search or press Enter to find the loan</li>
            <li>Use recent searches for quick access to previously viewed loans</li>
          </ul>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
