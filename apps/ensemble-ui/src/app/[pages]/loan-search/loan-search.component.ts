import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormControl, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil, debounceTime, distinctUntilChanged, catchError, of } from 'rxjs';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';

// Services and models
import { LoanService } from '../../shared/services/loan.service';

// Define the loan response interface locally for now
interface LoanResponseDto {
  id: string;
  loanNumber: string;
  borrowerName: string;
  borrowerEmail?: string;
  loanAmount: number;
  status: string;
  applicationDate: string;
  propertyAddress: string;
}

@Component({
  selector: 'ens-loan-search',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatListModule,
    MatDividerModule,
    MatTooltipModule
  ],
  templateUrl: './loan-search.component.html',
  styleUrls: ['./loan-search.component.scss']
})
export class LoanSearchComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly router = inject(Router);
  private readonly loanService = inject(LoanService);

  // Form controls
  loanNumberControl = new FormControl('', [
    Validators.required,
    this.loanNumberValidator
  ]);

  // Component state
  isSearching = false;
  searchError: string | null = null;
  recentSearches: string[] = [];
  searchAttempts = 0;
  maxSearchAttempts = 5;

  ngOnInit(): void {
    this.loadRecentSearches();
    this.setupSearchDebounce();
  }

  /**
   * Custom validator for loan numbers
   */
  private loanNumberValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) {
      return null; // Let required validator handle empty values
    }

    // Remove any non-digit characters for validation
    const cleanValue = value.replace(/\D/g, '');

    if (cleanValue.length !== 10) {
      return { invalidLength: { actualLength: cleanValue.length, requiredLength: 10 } };
    }

    // Check for obviously invalid patterns (all same digit, sequential, etc.)
    if (/^(\d)\1{9}$/.test(cleanValue)) {
      return { invalidPattern: { message: 'Loan number cannot be all the same digit' } };
    }

    if (cleanValue === '1234567890' || cleanValue === '0123456789') {
      return { invalidPattern: { message: 'Please enter a valid loan number' } };
    }

    return null;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load recent searches from the service
   */
  private loadRecentSearches(): void {
    this.loanService.recentSearches$
      .pipe(takeUntil(this.destroy$))
      .subscribe(searches => {
        this.recentSearches = searches;
      });
  }

  /**
   * Setup debounced search for better UX
   */
  private setupSearchDebounce(): void {
    this.loanNumberControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        if (value && this.loanNumberControl.valid) {
          // Auto-search when valid loan number is entered
          // This is optional - you might want to require explicit search button click
        }
      });
  }

  /**
   * Perform loan search
   */
  search(): void {
    if (this.loanNumberControl.invalid) {
      this.loanNumberControl.markAsTouched();
      return;
    }

    // Rate limiting check
    if (this.searchAttempts >= this.maxSearchAttempts) {
      this.searchError = 'Too many search attempts. Please wait a moment before trying again.';
      return;
    }

    const loanNumber = this.loanNumberControl.value?.trim();
    if (!loanNumber) {
      return;
    }

    this.isSearching = true;
    this.searchError = null;
    this.searchAttempts++;

    // Reset search attempts after 1 minute
    setTimeout(() => {
      this.searchAttempts = Math.max(0, this.searchAttempts - 1);
    }, 60000);

    this.loanService.findLoanByNumber(loanNumber)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          this.handleSearchError(error);
          return of(null);
        })
      )
      .subscribe({
        next: (loan: LoanResponseDto | null) => {
          this.isSearching = false;
          if (loan) {
            this.navigateToLoanDetails(loan);
          }
        }
      });
  }

  /**
   * Navigate to loan details page
   */
  private navigateToLoanDetails(loan: LoanResponseDto): void {
    this.router.navigate(['/loan-details', loan.id]);
  }

  /**
   * Handle search errors
   */
  private handleSearchError(error: any): void {
    this.isSearching = false;

    if (error.status === 404) {
      this.searchError = 'Loan not found. Please verify the loan number and try again.';
    } else if (error.status === 0) {
      this.searchError = 'Unable to connect to the server. Please check your internet connection and try again.';
    } else if (error.status === 429) {
      this.searchError = 'Too many requests. Please wait a moment before searching again.';
    } else if (error.status === 500) {
      this.searchError = 'Server error occurred. Please try again later or contact support.';
    } else if (error.status === 403) {
      this.searchError = 'Access denied. You may not have permission to view this loan.';
    } else {
      this.searchError = `An unexpected error occurred (${error.status || 'Unknown'}). Please try again.`;
    }

    // Log error for debugging (in production, this would go to a logging service)
    console.error('Loan search error:', error);
  }

  /**
   * Select a recent search
   */
  selectRecentSearch(loanNumber: string): void {
    this.loanNumberControl.setValue(loanNumber);
    this.search();
  }

  /**
   * Clear recent searches
   */
  clearRecentSearches(): void {
    this.loanService.clearRecentSearches();
  }

  /**
   * Clear the search input
   */
  clearSearch(): void {
    this.loanNumberControl.reset();
    this.searchError = null;
  }

  /**
   * Get error message for the form field
   */
  getErrorMessage(): string {
    if (this.loanNumberControl.hasError('required')) {
      return 'Loan number is required';
    }
    if (this.loanNumberControl.hasError('invalidLength')) {
      const error = this.loanNumberControl.getError('invalidLength');
      return `Loan number must be exactly ${error.requiredLength} digits (currently ${error.actualLength})`;
    }
    if (this.loanNumberControl.hasError('invalidPattern')) {
      const error = this.loanNumberControl.getError('invalidPattern');
      return error.message;
    }
    return '';
  }

  /**
   * Format loan number for display
   */
  formatLoanNumber(loanNumber: string): string {
    return this.loanService.formatLoanNumber(loanNumber);
  }

  /**
   * Handle Enter key press in search input
   */
  onEnterKey(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.search();
    }
  }

  /**
   * TrackBy function for recent searches list
   */
  trackByLoanNumber(_index: number, loanNumber: string): string {
    return loanNumber;
  }

  /**
   * Format input as user types (add dashes for readability)
   */
  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/\D/g, ''); // Remove non-digits

    // Limit to 10 digits
    if (value.length > 10) {
      value = value.substring(0, 10);
    }

    // Update the form control with clean value
    this.loanNumberControl.setValue(value, { emitEvent: false });

    // Update the input display with formatted value
    input.value = this.formatLoanNumberForDisplay(value);
  }

  /**
   * Format loan number for display (with dashes)
   */
  private formatLoanNumberForDisplay(value: string): string {
    if (value.length <= 3) return value;
    if (value.length <= 6) return `${value.slice(0, 3)}-${value.slice(3)}`;
    return `${value.slice(0, 3)}-${value.slice(3, 6)}-${value.slice(6)}`;
  }

  /**
   * Check if search button should be disabled
   */
  isSearchDisabled(): boolean {
    return this.isSearching ||
           this.loanNumberControl.invalid ||
           this.searchAttempts >= this.maxSearchAttempts ||
           !this.loanNumberControl.value?.trim();
  }

  /**
   * Get remaining search attempts
   */
  getRemainingAttempts(): number {
    return Math.max(0, this.maxSearchAttempts - this.searchAttempts);
  }

  /**
   * Check if rate limit warning should be shown
   */
  shouldShowRateLimitWarning(): boolean {
    return this.searchAttempts >= this.maxSearchAttempts - 1;
  }

  /**
   * Remove a specific loan number from recent searches
   */
  removeFromRecent(loanNumber: string, event: Event): void {
    event.stopPropagation(); // Prevent triggering the search
    this.loanService.removeFromRecentSearches(loanNumber);
  }

}
