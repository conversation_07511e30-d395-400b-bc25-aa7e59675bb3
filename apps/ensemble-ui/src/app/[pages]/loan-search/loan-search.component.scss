// Minimal, seamless loan search design
.loan-search-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 3rem 2rem;
  min-height: 100vh;
  background: transparent; // Match parent background
  position: relative;
  transition: opacity 0.3s ease;

  &.searching {
    .search-form,
    .recent-searches,
    .search-tips {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}

// Minimal search form - no cards, no shadows
.search-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 3rem;

  .search-input-container {
    width: 100%;
    max-width: 500px;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;

    .search-input {
      flex: 1;

      ::ng-deep {
        .mat-mdc-form-field-wrapper {
          background: transparent;
        }

        .mat-mdc-form-field-outline {
          color: rgba(95, 99, 104, 0.2);
          border-radius: 24px;
        }

        .mat-mdc-form-field-outline-thick {
          color: #1976d2;
          border-radius: 24px;
        }

        .mdc-text-field {
          background: transparent;
          border-radius: 24px;
        }

        .mdc-text-field--filled {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 24px;
        }

        .mat-mdc-form-field-hint-wrapper,
        .mat-mdc-form-field-error-wrapper {
          padding: 0 16px;
        }
      }
    }

    .search-button {
      border-radius: 24px;
      padding: 0 24px;
      height: 48px;
      font-weight: 500;
      text-transform: none;
      flex-shrink: 0;

      &[disabled] {
        opacity: 0.5;
      }

      mat-spinner {
        margin-right: 8px;
      }
    }

    .clear-button {
      border-radius: 50%;
      width: 40px;
      height: 40px;
      min-width: 40px;
      color: #5f6368;
      flex-shrink: 0;
      opacity: 0.7;
      transition: opacity 0.2s ease, background-color 0.2s ease;

      &:hover {
        background-color: rgba(95, 99, 104, 0.08);
        opacity: 1;
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  .search-error {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 1rem;
    padding: 12px 16px;
    background: rgba(244, 67, 54, 0.1);
    border-radius: 24px;
    color: #d32f2f;
    font-size: 14px;
    max-width: 400px;

    mat-icon {
      color: #d32f2f;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }

  .rate-limit-warning {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 1rem;
    background: #fff3e0;
    border-radius: 4px;
    color: #ef6c00;

    mat-icon {
      color: #ef6c00;
    }
  }
}

// Minimal recent searches - no cards
.recent-searches {
  margin-bottom: 2rem;

  .recent-searches-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 1rem;
    color: #5f6368;
    font-size: 14px;
    font-weight: 500;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      color: #5f6368;
    }

    .clear-all-button {
      margin-left: auto;
      border-radius: 16px;
      padding: 4px 12px;
      font-size: 12px;
      min-height: 28px;
      color: #5f6368;

      &:hover {
        background-color: rgba(95, 99, 104, 0.08);
      }
    }
  }

  .recent-search-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 16px;
    margin: 4px auto;
    max-width: 300px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.05);
    color: #5f6368;
    font-size: 14px;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #202124;
    }

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      color: inherit;
    }

    .loan-number {
      flex: 1;
      text-align: center;
    }

    .remove-button {
      border-radius: 50%;
      width: 24px;
      height: 24px;
      min-width: 24px;
      padding: 0;
      opacity: 0;
      transition: opacity 0.2s ease;

      mat-icon {
        font-size: 14px;
        width: 14px;
        height: 14px;
      }
    }

    &:hover .remove-button {
      opacity: 1;
    }
  }
}

// Minimal search tips
.search-tips {
  text-align: center;
  color: #5f6368;
  font-size: 13px;
  line-height: 1.4;
  max-width: 400px;
  margin: 0 auto;

  .tip-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    margin-bottom: 8px;
    color: #5f6368;
  }

  .tip-text {
    margin: 0;
  }
}

// Responsive design
@media (max-width: 768px) {
  .loan-search-container {
    padding: 2rem 1rem;
  }

  .search-form {
    .search-input-container {
      max-width: 100%;
    }

    .search-actions {
      flex-direction: column;
      gap: 1rem;

      .search-button {
        width: 100%;
        max-width: 300px;
      }

      .clear-button {
        align-self: center;
      }
    }
  }

  .recent-searches {
    .recent-search-item {
      max-width: 100%;
    }
  }
}

// Enhanced form field styling for minimal design
.search-input {
  ::ng-deep {
    .mat-mdc-form-field-hint-wrapper {
      color: #ff9800;
      font-size: 12px;
      text-align: center;
    }

    .mat-mdc-form-field-error-wrapper {
      font-size: 12px;
      text-align: center;
    }

    .mdc-text-field--focused {
      .mdc-floating-label {
        color: #1976d2 !important;
      }
    }

    .mdc-text-field--invalid {
      .mdc-floating-label {
        color: #f44336 !important;
      }
    }

    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 8px;
    }
  }
}

// Smooth animations
.search-error {
  animation: slideIn 0.3s ease-out;
}

.recent-search-item {
  animation: fadeIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Pulse animation for rate limit warning
.rate-limit-warning {
  animation: slideIn 0.3s ease-out, pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}
