<div class="loan-details-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading loan details...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <h2>{{ error }}</h2>
    <button mat-raised-button color="primary" (click)="goBack()">
      <mat-icon>arrow_back</mat-icon>
      Back to Search
    </button>
  </div>

  <!-- Loan Details -->
  <div *ngIf="loan && !isLoading" class="loan-content">
    <!-- Header -->
    <div class="loan-header">
      <button mat-icon-button (click)="goBack()" class="back-button">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <div class="header-info">
        <h1>Loan {{ loan.loanNumber }}</h1>
        <mat-chip [color]="getStatusColor(loan.status)" selected>
          {{ getStatusText(loan.status) }}
        </mat-chip>
      </div>
    </div>

    <!-- Borrower Information -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>person</mat-icon>
          Borrower Information
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-item">
            <label>Name</label>
            <span>{{ loan.borrowerName }}</span>
          </div>
          <div class="info-item" *ngIf="loan.borrowerEmail">
            <label>Email</label>
            <span>{{ loan.borrowerEmail }}</span>
          </div>
          <div class="info-item">
            <label>Annual Income</label>
            <span>{{ formatCurrency(loan.annualIncome) }}</span>
          </div>
          <div class="info-item" *ngIf="loan.creditScore">
            <label>Credit Score</label>
            <span>{{ loan.creditScore }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Loan Information -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>account_balance</mat-icon>
          Loan Information
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-item">
            <label>Loan Amount</label>
            <span class="amount">{{ formatCurrency(loan.loanAmount) }}</span>
          </div>
          <div class="info-item">
            <label>Loan Type</label>
            <span>{{ loan.loanType }}</span>
          </div>
          <div class="info-item">
            <label>Monthly Payment</label>
            <span class="amount">{{ formatCurrency(loan.monthlyPayment) }}</span>
          </div>
          <div class="info-item">
            <label>Application Date</label>
            <span>{{ formatDate(loan.applicationDate) }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Property Information -->
    <mat-card class="info-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>home</mat-icon>
          Property Information
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="info-grid">
          <div class="info-item full-width">
            <label>Property Address</label>
            <span>{{ loan.propertyAddress }}</span>
          </div>
          <div class="info-item">
            <label>Property Value</label>
            <span class="amount">{{ formatCurrency(loan.propertyValue) }}</span>
          </div>
          <div class="info-item">
            <label>Down Payment</label>
            <span class="amount">{{ formatCurrency(loan.downPayment) }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Actions -->
    <div class="actions-container">
      <button mat-raised-button color="primary" (click)="goBack()">
        <mat-icon>search</mat-icon>
        New Search
      </button>
    </div>
  </div>
</div>
