import { Component, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

// Angular Material imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';

// Services
import { LoanService } from '../../shared/services/loan.service';

// Local interface for loan data
interface LoanResponseDto {
  id: string;
  loanNumber: string;
  borrowerName: string;
  borrowerEmail?: string;
  loanAmount: number;
  status: string;
  applicationDate: string;
  propertyAddress: string;
  propertyValue: number;
  downPayment: number;
  monthlyPayment: number;
  loanType: string;
  creditScore?: number;
  annualIncome: number;
}

@Component({
  selector: 'ens-loan-details',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule
  ],
  templateUrl: './loan-details.component.html',
  styleUrls: ['./loan-details.component.scss']
})
export class LoanDetailsComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly loanService = inject(LoanService);

  loan: LoanResponseDto | null = null;
  isLoading = true;
  error: string | null = null;

  ngOnInit(): void {
    this.route.params
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        const loanId = params['id'];
        if (loanId) {
          this.loadLoan(loanId);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadLoan(loanId: string): void {
    this.isLoading = true;
    this.error = null;

    this.loanService.findLoanById(loanId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (loan) => {
          this.loan = loan;
          this.isLoading = false;
        },
        error: (error) => {
          this.isLoading = false;
          this.handleError(error);
        }
      });
  }

  private handleError(error: any): void {
    if (error.status === 404) {
      this.error = 'Loan not found.';
    } else {
      this.error = 'Failed to load loan details. Please try again.';
    }
  }

  goBack(): void {
    this.router.navigate(['/loan-search']);
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getStatusColor(status: string): string {
    const statusColors: { [key: string]: string } = {
      'draft': 'default',
      'submitted': 'primary',
      'under_review': 'accent',
      'approved': 'primary',
      'denied': 'warn',
      'funded': 'primary',
      'closed': 'default',
      'cancelled': 'warn'
    };
    return statusColors[status] || 'default';
  }

  getStatusText(status: string): string {
    const statusTexts: { [key: string]: string } = {
      'draft': 'Draft',
      'submitted': 'Submitted',
      'under_review': 'Under Review',
      'approved': 'Approved',
      'denied': 'Denied',
      'funded': 'Funded',
      'closed': 'Closed',
      'cancelled': 'Cancelled'
    };
    return statusTexts[status] || status;
  }
}
