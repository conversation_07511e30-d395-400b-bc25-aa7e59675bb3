.loan-details-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  gap: 1rem;

  p {
    color: #666;
    font-size: 1.1rem;
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  gap: 1rem;
  text-align: center;

  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
  }

  h2 {
    color: #666;
    margin: 0;
  }
}

.loan-content {
  .loan-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .back-button {
      color: #1976d2;
    }

    .header-info {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;

      h1 {
        margin: 0;
        color: #333;
        font-size: 1.8rem;
        font-weight: 500;
      }
    }
  }

  .info-card {
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

    mat-card-header {
      mat-card-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.3rem;
        color: #333;

        mat-icon {
          color: #1976d2;
        }
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;
      margin-top: 1rem;

      .info-item {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;

        &.full-width {
          grid-column: 1 / -1;
        }

        label {
          font-size: 0.9rem;
          color: #666;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        span {
          font-size: 1.1rem;
          color: #333;
          font-weight: 400;

          &.amount {
            font-weight: 600;
            color: #1976d2;
          }
        }
      }
    }
  }

  .actions-container {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    padding: 1rem;

    button {
      min-width: 150px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .loan-details-container {
    padding: 1rem;
  }

  .loan-content {
    .loan-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;

      .header-info {
        width: 100%;
        justify-content: space-between;

        h1 {
          font-size: 1.5rem;
        }
      }
    }

    .info-card {
      .info-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .info-item {
          &.full-width {
            grid-column: 1;
          }
        }
      }
    }
  }
}

// Status chip colors
::ng-deep {
  .mat-mdc-chip {
    &.mat-primary {
      background-color: #1976d2;
      color: white;
    }

    &.mat-accent {
      background-color: #ff9800;
      color: white;
    }

    &.mat-warn {
      background-color: #f44336;
      color: white;
    }
  }
}

// Animation for content loading
.loan-content {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
