# Pages

This directory contains the main page components that serve as entry points for different features of the application. Each page is lazily loaded through Angular routes.

## Overview

Pages are top-level components that:
- Represent a complete screen or feature
- Are directly accessible via specific routes
- Combine multiple containers and components to deliver functionality
- Handle layout and orchestration of feature components

## Structure

Each page is organized in its own directory and follows this structure:

```
[pages]/
├── loan-search/
│   ├── loan-search.component.html
│   ├── loan-search.component.scss
│   └── loan-search.component.ts
├── loan-status/
│   ├── loan-status.component.html
│   ├── loan-status.component.scss
│   └── loan-status.component.ts
├── log/
│   ├── log.component.html
│   ├── log.component.scss
│   └── log.component.ts
└── (other page components)
```

## Pages Description

### Loan Search

The Loan Search page allows users to search for loans by various identifiers such as loan number, borrower name, or application ID.

**Key Features:**
- Search form with multiple criteria
- Results table with filtering and sorting
- Quick actions for common operations
- Detailed search filters

### Loan Status

The Loan Status page shows detailed information about a specific loan, including its current state across multiple systems.

**Key Features:**
- Overview of loan details
- Status indicators for each system
- Tabbed interface for different data categories
- Quick actions for common operations
- Inline editing capabilities

### Activity Log

The Activity Log page provides a system-wide view of important events and user actions.

**Key Features:**
- Filterable log entries
- Timeline visualization
- User activity tracking
- System event monitoring

## Development Guidelines

When creating new pages:

1. Use standalone components with explicit imports
2. Follow the existing directory structure and naming convention
3. Implement lazy loading through the Angular router
4. Use containers for data fetching and business logic
5. Use presentational components for UI rendering
6. Add thorough component documentation
7. Include unit and integration tests