.page-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}

.loan-header {
  margin-bottom: 24px;
  
  h1 {
    margin-bottom: 8px;
    color: #3f51b5;
  }
  
  p {
    margin: 0;
    color: rgba(0, 0, 0, 0.6);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  
  p {
    margin-top: 24px;
    font-size: 18px;
    color: rgba(0, 0, 0, 0.6);
  }
}

.systems-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
  gap: 24px;
}

.system-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  
  &.status-active {
    background-color: #c8e6c9;
    color: #2e7d32;
  }
  
  &.status-processing {
    background-color: #bbdefb;
    color: #1565c0;
  }
  
  &.status-complete {
    background-color: #d1c4e9;
    color: #4527a0;
  }
  
  &.status-pending {
    background-color: #ffecb3;
    color: #ff8f00;
  }
}

.update-timestamp {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.54);
  margin-bottom: 24px;
}

.fields-container {
  margin-top: 16px;
}

.field-row {
  display: grid;
  grid-template-columns: 150px 1fr;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  
  &:last-child {
    border-bottom: none;
  }
}

.field-label {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.87);
  align-self: center;
}

.field-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .field-timestamp {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.54);
    margin: 0 12px;
  }
  
  .edit-button {
    background-color: transparent;
    border: none;
    color: #3f51b5;
    cursor: pointer;
    font-size: 14px;
    padding: 4px 8px;
    
    &:hover {
      background-color: rgba(63, 81, 181, 0.04);
      border-radius: 4px;
    }
  }
}

.field-edit {
  display: flex;
  gap: 16px;
  
  input, select {
    flex: 1;
    padding: 8px;
    border: 1px solid rgba(0, 0, 0, 0.23);
    border-radius: 4px;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: #3f51b5;
    }
  }
  
  .edit-actions {
    display: flex;
    gap: 8px;
    
    button {
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 60px;
    }
    
    .save-button {
      background-color: #3f51b5;
      color: white;
      
      &:hover {
        background-color: darken(#3f51b5, 5%);
      }
      
      &:disabled {
        background-color: rgba(0, 0, 0, 0.12);
        color: rgba(0, 0, 0, 0.38);
        cursor: not-allowed;
      }
    }
    
    .cancel-button {
      background-color: transparent;
      color: rgba(0, 0, 0, 0.87);
      border: 1px solid rgba(0, 0, 0, 0.23);
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
      
      &:disabled {
        color: rgba(0, 0, 0, 0.38);
        cursor: not-allowed;
      }
    }
  }
}

.inline-spinner {
  margin: 0;
}