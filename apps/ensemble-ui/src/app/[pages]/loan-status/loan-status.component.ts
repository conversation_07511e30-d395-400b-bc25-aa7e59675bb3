import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RktAlertModule } from '@rocketcentral/rocket-design-system-angular';
import { LoanStatusService, LoanStatus, SystemStatus, EditableField } from '../../shared/services/loan-status.service';
import { NotificationService } from '../../shared/services/notification.service';
import { LogService } from '../../shared/services/log.service';
import { NotificationComponent } from '../../shared/components/notification/notification.component';

@Component({
  selector: 'ens-loan-status',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    RktAlertModule,
    NotificationComponent
  ],
  templateUrl: './loan-status.component.html',
  styleUrls: ['./loan-status.component.scss']
})
export class LoanStatusComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly loanStatusService = inject(LoanStatusService);
  private readonly notificationService = inject(NotificationService);
  private readonly logService = inject(LogService);

  loanNumber = '';
  loanStatus: LoanStatus | null = null;
  isLoading = true;
  loadingStates: {[key: string]: boolean} = {};
  editStates: {[key: string]: {editing: boolean, system: string, field: EditableField, originalValue: string}} = {};
  
  ngOnInit(): void {
    this.loanNumber = this.route.snapshot.paramMap.get('loanNumber') || '';
    this.loadLoanStatus();
  }
  
  loadLoanStatus(): void {
    if (!this.loanNumber) {
      this.router.navigate(['/loan-search']);
      return;
    }
    
    this.isLoading = true;
    
    this.loanStatusService.getLoanStatus(this.loanNumber).subscribe({
      next: (status) => {
        this.loanStatus = status;
        this.isLoading = false;
        this.logService.logSystemEvent(`Loaded loan status for ${this.loanNumber}`);
      },
      error: () => {
        this.isLoading = false;
        this.router.navigate(['/loan-search']);
      }
    });
  }
  
  startEditing(system: string, field: EditableField): void {
    if (!field.editable) return;
    
    const key = `${system}-${field.name}`;
    this.editStates[key] = {
      editing: true,
      system,
      field,
      originalValue: field.value
    };
  }
  
  cancelEditing(key: string): void {
    const editState = this.editStates[key];
    if (!editState) return;
    
    editState.field.value = editState.originalValue;
    delete this.editStates[key];
  }
  
  saveField(key: string): void {
    const editState = this.editStates[key];
    if (!editState) return;
    
    const { system, field, originalValue } = editState;
    
    if (field.value === originalValue) {
      delete this.editStates[key];
      return;
    }
    
    this.loadingStates[key] = true;
    
    this.loanStatusService.updateField(this.loanNumber, system, field.name, field.value).subscribe({
      next: () => {
        delete this.editStates[key];
        this.loadingStates[key] = false;
        field.lastUpdated = new Date();
        this.logService.logUpdate(system, field.name, originalValue, field.value, this.loanNumber);
      },
      error: () => {
        field.value = originalValue;
        delete this.editStates[key];
        this.loadingStates[key] = false;
      }
    });
  }
  
  goBack(): void {
    this.router.navigate(['/loan-search']);
  }
  
  refreshStatus(): void {
    this.loadLoanStatus();
  }

  viewChangeHistory(): void {
    this.router.navigate(['/change-history', this.loanNumber]);
  }
  
  formatTimestamp(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - new Date(timestamp).getTime();
    
    // Less than a minute
    if (diff < 60000) {
      return 'Just now';
    }
    
    // Less than an hour
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    }
    
    // Less than a day
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    }
    
    // More than a day
    const days = Math.floor(diff / 86400000);
    if (days < 14) {
      return `${days} ${days === 1 ? 'day' : 'days'} ago`;
    }
    
    // Default to date
    return new Date(timestamp).toLocaleString();
  }
}