<div class="page-container">
  <ens-notification></ens-notification>
  
  <div class="header-actions">
    <button 
      class="rkt-Button rkt-Button--has-icon-left" 
      mat-flat-button 
      color="primary"
      (click)="goBack()"
    >
      <mat-icon class="rkt-Icon">←</mat-icon>
      Back to Search
    </button>
    
    <button
      class="rkt-Button"
      mat-flat-button
      color="primary"
      [disabled]="isLoading"
      (click)="refreshStatus()"
    >
      Refresh
    </button>

    <button
      class="rkt-Button rkt-Button--has-icon-left"
      mat-stroked-button
      color="accent"
      (click)="viewChangeHistory()"
    >
      <mat-icon class="rkt-Icon">history</mat-icon>
      Change History
    </button>
  </div>
  
  <div class="loan-header">
    <h1>Loan Status: {{ loanNumber }}</h1>
    <p *ngIf="loanStatus">Last updated: {{ formatTimestamp(loanStatus.lastFetched) }}</p>
  </div>
  
  <!-- Loading state -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner aria-label="Loading loan status" diameter="90" class="rkt-ProgressSpinner"></mat-spinner>
    <p>Loading loan data from multiple systems...</p>
  </div>
  
  <!-- Loaded state -->
  <div class="systems-container" *ngIf="!isLoading && loanStatus">
    @for (systemStatus of loanStatus!.systems; track systemStatus.system) {
      <mat-card class="rkt-Card rkt-Card--tall">
        <div class="system-header">
          <h3 class="rkt-Card__heading">{{ systemStatus.system }}</h3>
          <div class="status-badge" [ngClass]="'status-' + systemStatus.statusCode.toLowerCase()">
            {{ systemStatus.statusCode }}
          </div>
        </div>
        
        <p class="rkt-Card__subtitle">{{ systemStatus.statusDescription }}</p>
        <p class="update-timestamp">Last updated: {{ formatTimestamp(systemStatus.lastUpdated) }}</p>
        
        <div class="fields-container">
          @for (field of systemStatus.editableFields; track field.name) {
            <div class="field-row">
              <div class="field-label">{{ field.name }}</div>
              
              <!-- View mode -->
              <div class="field-value" *ngIf="!editStates[systemStatus.system + '-' + field.name]">
                <span>{{ field.value }}</span>
                <span class="field-timestamp">{{ formatTimestamp(field.lastUpdated) }}</span>
                
                <button 
                  *ngIf="field.editable"
                  class="edit-button" 
                  (click)="startEditing(systemStatus.system, field)"
                >
                  Edit
                </button>
              </div>
              
              <!-- Edit mode -->
              <div class="field-edit" *ngIf="editStates[systemStatus.system + '-' + field.name]">
                <div [ngSwitch]="field.type">
                  <!-- Text input -->
                  <input 
                    *ngSwitchCase="'text'"
                    type="text" 
                    [(ngModel)]="field.value"
                  >
                  
                  <!-- Number input -->
                  <input 
                    *ngSwitchCase="'number'"
                    type="number" 
                    [(ngModel)]="field.value"
                  >
                  
                  <!-- Date input -->
                  <input 
                    *ngSwitchCase="'date'"
                    type="date" 
                    [(ngModel)]="field.value"
                  >
                  
                  <!-- Select input -->
                  <select 
                    *ngSwitchCase="'select'"
                    [(ngModel)]="field.value"
                  >
                    <option *ngFor="let option of field.options" [value]="option">
                      {{ option }}
                    </option>
                  </select>
                </div>
                
                <div class="edit-actions">
                  <button 
                    class="save-button"
                    [disabled]="loadingStates[systemStatus.system + '-' + field.name]"
                    (click)="saveField(systemStatus.system + '-' + field.name)"
                  >
                    <span *ngIf="!loadingStates[systemStatus.system + '-' + field.name]">Save</span>
                    <mat-spinner 
                      *ngIf="loadingStates[systemStatus.system + '-' + field.name]"
                      diameter="16"
                      class="inline-spinner"
                    ></mat-spinner>
                  </button>
                  
                  <button 
                    class="cancel-button"
                    [disabled]="loadingStates[systemStatus.system + '-' + field.name]"
                    (click)="cancelEditing(systemStatus.system + '-' + field.name)"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          }
        </div>
      </mat-card>
    }
  </div>
  
  <!-- Error state -->
  <div *ngIf="!isLoading && !loanStatus">
    <rkt-alert variant="warn" [isDismissible]="false">
      <p class="rkt-Alert__text">
        Unable to load loan status data. Please try again later or check the loan number.
      </p>
    </rkt-alert>
  </div>
</div>