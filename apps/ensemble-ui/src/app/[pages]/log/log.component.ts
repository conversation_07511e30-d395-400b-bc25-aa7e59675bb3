import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { LogService, LogEntry } from '../../shared/services/log.service';
import { NotificationService } from '../../shared/services/notification.service';
import { NotificationComponent } from '../../shared/components/notification/notification.component';

@Component({
  selector: 'ens-log',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatIconModule,
    NotificationComponent
  ],
  templateUrl: './log.component.html',
  styleUrls: ['./log.component.scss']
})
export class LogComponent {
  private readonly logService = inject(LogService);
  private readonly notificationService = inject(NotificationService);

  selectedCategory: 'all' | 'search' | 'update' | 'error' | 'system' = 'all';
  logs = this.logService.getLogs();
  
  get filteredLogs() {
    if (this.selectedCategory === 'all') {
      return this.logs();
    }
    return this.logs().filter(log => log.category === this.selectedCategory);
  }
  
  clearLogs(): void {
    this.logService.clearLogs();
    this.notificationService.success('Logs cleared successfully');
  }
  
  getCategoryClasses(category: string): string {
    switch (category) {
      case 'search':
        return 'category-search';
      case 'update':
        return 'category-update';
      case 'error':
        return 'category-error';
      case 'system':
        return 'category-system';
      default:
        return '';
    }
  }
  
  formatTimestamp(timestamp: Date): string {
    return new Date(timestamp).toLocaleString();
  }
}