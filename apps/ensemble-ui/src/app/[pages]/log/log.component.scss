.page-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h1 {
    margin: 0;
    color: #3f51b5;
  }
}

.log-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 8px;
  
  label {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.6);
  }
  
  select {
    padding: 8px 12px;
    border: 1px solid rgba(0, 0, 0, 0.23);
    border-radius: 4px;
    background-color: white;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: #3f51b5;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 48px 0;
  
  .empty-icon {
    font-size: 64px;
    height: 64px;
    width: 64px;
    color: rgba(0, 0, 0, 0.3);
    margin-bottom: 16px;
  }
  
  h2 {
    margin: 0 0 16px 0;
    color: rgba(0, 0, 0, 0.6);
  }
  
  p {
    color: rgba(0, 0, 0, 0.5);
  }
}

.log-table {
  width: 100%;
  border-collapse: collapse;
  
  th, td {
    text-align: left;
    padding: 12px 16px;
  }
  
  th {
    background-color: #f5f5f5;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.7);
    position: sticky;
    top: 0;
  }
  
  tr {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .timestamp-cell {
    white-space: nowrap;
    width: 180px;
  }
}

.category-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
  
  &.category-search {
    background-color: #bbdefb;
    color: #0d47a1;
  }
  
  &.category-update {
    background-color: #c8e6c9;
    color: #1b5e20;
  }
  
  &.category-error {
    background-color: #ffcdd2;
    color: #b71c1c;
  }
  
  &.category-system {
    background-color: #e1bee7;
    color: #4a148c;
  }
}