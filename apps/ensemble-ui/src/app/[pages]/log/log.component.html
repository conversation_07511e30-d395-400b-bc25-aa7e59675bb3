<div class="page-container">
  <ens-notification></ens-notification>
  
  <div class="log-header">
    <h1>Activity Log</h1>
    
    <div class="log-actions">
      <div class="filter-container">
        <label for="category-filter">Filter by category:</label>
        <select id="category-filter" [(ngModel)]="selectedCategory">
          <option value="all">All Categories</option>
          <option value="search">Search</option>
          <option value="update">Update</option>
          <option value="error">Error</option>
          <option value="system">System</option>
        </select>
      </div>
      
      <button 
        class="rkt-Button rkt-Button--secondary" 
        mat-flat-button 
        color="accent"
        (click)="clearLogs()"
        [disabled]="filteredLogs.length === 0"
      >
        Clear Logs
      </button>
    </div>
  </div>
  
  <!-- Empty state -->
  <div class="empty-state" *ngIf="filteredLogs.length === 0">
    <mat-card class="rkt-Card rkt-Card--tall">
      <mat-icon class="empty-icon">description</mat-icon>
      <h2>No logs found</h2>
      <p *ngIf="selectedCategory !== 'all'">Try selecting a different category filter.</p>
      <p *ngIf="selectedCategory === 'all'">No activity has been logged yet.</p>
    </mat-card>
  </div>
  
  <!-- Log entries -->
  <div class="log-entries" *ngIf="filteredLogs.length > 0">
    <mat-card class="rkt-Card">
      <table class="log-table">
        <thead>
          <tr>
            <th>Timestamp</th>
            <th>Category</th>
            <th>Action</th>
            <th>Details</th>
          </tr>
        </thead>
        <tbody>
          @for (log of filteredLogs; track log.id) {
            <tr>
              <td class="timestamp-cell">{{ formatTimestamp(log.timestamp) }}</td>
              <td>
                <span class="category-badge" [ngClass]="getCategoryClasses(log.category)">
                  {{ log.category }}
                </span>
              </td>
              <td>{{ log.action }}</td>
              <td>{{ log.details }}</td>
            </tr>
          }
        </tbody>
      </table>
    </mat-card>
  </div>
</div>