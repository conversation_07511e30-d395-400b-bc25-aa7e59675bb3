<div class="change-history-page">
  <!-- Page Header -->
  <mat-toolbar class="page-toolbar">
    <button 
      mat-icon-button 
      class="rkt-Button rkt-Button--icon"
      (click)="navigateBack()"
      matTooltip="Back to Loan Status"
    >
      <mat-icon>arrow_back</mat-icon>
    </button>
    
    <span class="page-title">Change History - Loan {{ loanNumber }}</span>
    
    <span class="spacer"></span>
    
    <button 
      mat-stroked-button 
      class="rkt-Button rkt-Button--secondary"
      (click)="navigateToLoanSearch()"
    >
      <mat-icon>search</mat-icon>
      Loan Search
    </button>
  </mat-toolbar>

  <!-- Page Content -->
  <div class="page-content">
    <ens-change-history
      [entityId]="loanNumber"
      [entityType]="entityType"
      [showFilters]="true"
      [autoRefresh]="true"
      [refreshInterval]="30000"
    ></ens-change-history>
  </div>
</div>
