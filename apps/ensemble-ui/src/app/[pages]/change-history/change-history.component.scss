.change-history-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-toolbar {
  background-color: #1976d2;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;

  .page-title {
    font-size: 1.25rem;
    font-weight: 500;
    margin-left: 1rem;
  }

  .spacer {
    flex: 1;
  }

  button {
    color: white;
    border-color: white;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  mat-icon {
    color: white;
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 1rem;
}

// Responsive Design
@media (max-width: 768px) {
  .page-toolbar {
    .page-title {
      font-size: 1rem;
      margin-left: 0.5rem;
    }

    button {
      .mat-button-wrapper {
        display: flex;
        align-items: center;
        gap: 0.25rem;
      }

      span:not(.mat-button-wrapper) {
        display: none;
      }
    }
  }

  .page-content {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .page-toolbar {
    .page-title {
      font-size: 0.875rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
