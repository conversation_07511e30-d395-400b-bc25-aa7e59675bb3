import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';

import { ChangeHistoryComponent as ChangeHistoryContainer } from '../../change-history/[containers]/change-history/change-history.component';
import { EntityType } from '@rocket-logic-ensemble/models';

@Component({
  selector: 'ens-change-history-page',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    ChangeHistoryContainer
  ],
  templateUrl: './change-history.component.html',
  styleUrl: './change-history.component.scss'
})
export class ChangeHistoryComponent implements OnInit {
  loanNumber: string = '';
  entityType = EntityType.LOAN;

  constructor(
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit() {
    this.route.params.subscribe(params => {
      this.loanNumber = params['loanNumber'];
    });
  }

  navigateBack() {
    this.router.navigate(['/loan-status', this.loanNumber]);
  }

  navigateToLoanSearch() {
    this.router.navigate(['/loan-search']);
  }
}
