{"name": "ensemble-bff", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ensemble-bff/src", "projectType": "application", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/ensemble-bff", "main": "apps/ensemble-bff/src/main.ts", "tsConfig": "apps/ensemble-bff/tsconfig.app.json", "assets": ["apps/ensemble-bff/src/assets"]}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "ensemble-bff:build", "inspect": false}, "configurations": {"development": {"buildTarget": "ensemble-bff:build"}, "production": {"buildTarget": "ensemble-bff:build"}}}, "lint": {"executor": "@nx/eslint:lint", "options": {"lintFilePatterns": ["apps/ensemble-bff/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/ensemble-bff/jest.config.ts"}}}, "tags": []}